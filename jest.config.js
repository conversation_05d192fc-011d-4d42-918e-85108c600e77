// jest.config.js
module.exports = {
  preset: 'jest-preset-angular',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: [
    '<rootDir>/src/setup-jest.ts',
    '<rootDir>/src/app/core/utils/__tests__/setup.ts'
  ],
  testMatch: ['<rootDir>/src/**/*.spec.ts'],
  moduleFileExtensions: ['ts', 'html', 'js', 'json'],
  transform: {
    '^.+\\.(ts|js|html)$': 'jest-preset-angular',
  },
  moduleNameMapper: {
    '^@utils/(.*)$': '<rootDir>/src/app/core/utils/$1',
    '^@directives/(.*)$': '<rootDir>/src/app/directives/$1',
    '^@environments/(.*)$': '<rootDir>/src/environments/$1',
    '^@guards/(.*)$': '<rootDir>/src/app/guards/$1',
    '^@models/(.*)$': '<rootDir>/src/app/models/$1',
    '^@pages/(.*)$': '<rootDir>/src/app/pages/$1',
    '^@services/(.*)$': '<rootDir>/src/app/services/$1',
    '^@shared/(.*)$': '<rootDir>/src/app/shared/$1',
    '^@config/(.*)$': '<rootDir>/src/app/config/$1',
    '^@metronic/(.*)$': '<rootDir>/src/app/_metronic/$1',
  },
  resolver: 'jest-preset-angular/build/resolvers/ng-jest-resolver.js',
  clearMocks: true,
  restoreMocks: true,
};
