# Backoffice Changelog - 2025

## [1.11.0](https://gitlab.com/maxxi-agro/atom/compare/v1.10.0...v1.11.0) (2025-01-20)


### Features:

* change menu reward ([2bc87fc](https://gitlab.com/maxxi-agro/atom/commit/2bc87fc9738e142523422a555a4b0ae5b87362ab))
* component image cropper ([079af82](https://gitlab.com/maxxi-agro/atom/commit/079af825462e2dc972a783e8eb1c0705572707d7))
* component image cropper ([3017647](https://gitlab.com/maxxi-agro/atom/commit/30176475a41a5eb5ca5524fbce4301280c68e439))
* embed video input - youtb iframe ([1bf0e18](https://gitlab.com/maxxi-agro/atom/commit/1bf0e1861fc1ffcfc0622a68ada2bdd18596b543))
* **MTMAI-6270, MTMAI-6277:** slicing level & retailer reward ([6759c3a](https://gitlab.com/maxxi-agro/atom/commit/6759c3aa5665506b576a22d2975f535ea4bac09e))
* **MTMAI-6282:** slicing detail product reward ([52e3675](https://gitlab.com/maxxi-agro/atom/commit/52e36754a921016157303347e64ce61631fff1d3))
* **MTMAI-6287:** form payload ([79fdbaf](https://gitlab.com/maxxi-agro/atom/commit/79fdbaf002febf053eb0b636609f011fab5033e5))
* **MTMAI-6287:** slicing form ([d9b1c3a](https://gitlab.com/maxxi-agro/atom/commit/d9b1c3ac4ef2260e95c1792cb500c7f70e9ab7f7))
* **MTMAI-6288, MTMAI-6283, MTMAI-6278:** integration form ([9990657](https://gitlab.com/maxxi-agro/atom/commit/99906574d1fd8d3cf8df867308ce713722cfd457))


### Bug Fixes:

* **Area Settings:** privilege cta area/team detail ([84b0cd8](https://gitlab.com/maxxi-agro/atom/commit/84b0cd83261d45327768c83bfe9e08455d02ba0f))
* card-tab-section handle delivery address card group ([f0451a7](https://gitlab.com/maxxi-agro/atom/commit/f0451a7cf7557e5a7a3f50bc03e2411b965de64c))
* **Detail Distributor:** PE admin marketing rbac ([fd42b85](https://gitlab.com/maxxi-agro/atom/commit/fd42b85c2b7a96794ecc85fe4abc4321aee75a0c))
* **Detail SO:** cta privilege create spm ([3270b9e](https://gitlab.com/maxxi-agro/atom/commit/3270b9e44db1e5683ed25f4bac87bc69e75de65d))
* **Detail User:** privilege cta activate user ([ad01a64](https://gitlab.com/maxxi-agro/atom/commit/ad01a646aed6ee42529afa3abd4f73cadb895119))
* **Detail User:** privilege cta user detail actions menu ([1c7c8a5](https://gitlab.com/maxxi-agro/atom/commit/1c7c8a546ca9a09efccc820aa4a01b4728774a7e))
* **image-cropper:** blob to file upload ([c0cdc67](https://gitlab.com/maxxi-agro/atom/commit/c0cdc678d9b69914354e44c35aa73f4d6f2e4272))
* **Product Catalog:** privilege cta add/download batch ([b9fed2e](https://gitlab.com/maxxi-agro/atom/commit/b9fed2e25f337eb6b948f133745af92819748983))
* **Profile Header:** render user status string ([8a99939](https://gitlab.com/maxxi-agro/atom/commit/8a9993957a13de5c47df96379451840c9d79f01d))
* **QR Product:** privilege cta add/download batch ([e2b7dae](https://gitlab.com/maxxi-agro/atom/commit/e2b7dae9daf9fefa8fd4473eacd91e1eeaf2ddf9))
* vercel script run env ([54b408f](https://gitlab.com/maxxi-agro/atom/commit/54b408ff62d0f7e0ca87600ee0096e2f66def6e9))
* wip tab-alamat-pengiriman ([7a72e76](https://gitlab.com/maxxi-agro/atom/commit/7a72e769569852de2e6fabf01c37ba27275bf2bd))