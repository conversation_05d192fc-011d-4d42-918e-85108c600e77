# GitLab CI for Branch Synchronization
# Automated sync: development to development-second

# Stage 1: Auto-sync development to development-second
sync-to-dev-second:
  stage: sync-dev-branches
  image: alpine:latest
  before_script:
    - apk add --no-cache git
    - git config --global user.name "GitLab CI"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
  script:
    - echo "Starting sync process"
    - git fetch origin
    - git checkout development-second
    - 'COMMIT_MSG="CHORE: auto-sync development into development-second [skip ci]"'
    - git merge origin/development --no-ff -m "$COMMIT_MSG"
    - git push origin development-second --follow-tags
    - echo "Sync completed successfully (with tags)"
  only:
    - development
  when: on_success

# Stage 2: Manual sync development to staging
sync-to-staging:
  stage: sync-to-staging
  image: alpine:latest
  before_script:
    - apk add --no-cache git
    - git config --global user.name "GitLab CI"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
  script:
    - echo "STAGING SYNC development to staging"
    - git fetch origin
    - git checkout staging
    - 'COMMIT_MSG="CHORE: manual-sync development into staging [skip ci]"'
    - git merge origin/development --no-ff -m "$COMMIT_MSG"
    - git push origin staging --follow-tags
    - echo "Successfully synced to staging (with tags)"
  when: manual
  only:
    - development

# Stage 3: Manual sync staging to main
sync-to-main:
  stage: sync-to-production
  image: alpine:latest
  before_script:
    - apk add --no-cache git
    - git config --global user.name "GitLab CI"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
  script:
    - echo "PRODUCTION SYNC staging to main"
    - git fetch origin
    - git checkout main
    - 'COMMIT_MSG="CHORE: production-sync staging into main [skip ci]"'
    - git merge origin/staging --no-ff -m "$COMMIT_MSG"
    - git push origin main --follow-tags
    - echo "Successfully synced to main (with tags for release creation)"
  when: manual
  only:
    - development

# Stage 4: Reverse sync development-second to development (emergency cases)
reverse-sync-dev-second-to-dev:
  stage: sync-dev-branches
  image: alpine:latest
  before_script:
    - apk add --no-cache git
    - git config --global user.name "GitLab CI Reverse Sync"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
  script:
    - echo "REVERSE SYNC development-second to development"
    - git fetch origin
    - git checkout development
    - git pull origin development

    # Check if there are commits in development-second not in development
    - COMMITS=$(git log development..development-second --pretty=format:"%H" --reverse)
    - |
      if [ -z "$COMMITS" ]; then
        echo "No commits to sync from development-second to development"
        exit 0
      fi

    # Show commits that will be cherry-picked
    - echo "Commits to be synced:"
    - git log development..development-second --oneline

    # Cherry-pick each commit
    - |
      for commit in $COMMITS; do
        echo "Cherry-picking commit :$commit"
        COMMIT_MSG=$(git log --format="%s" -n 1 $commit)
        echo "Commit message :$COMMIT_MSG"

        if git cherry-pick $commit; then
          echo "Successfully cherry-picked :$commit"
        else
          echo "CONFLICT detected in commit :$commit"
          echo "Commit message :$COMMIT_MSG"
          echo "Manual intervention required - stopping process"
          git cherry-pick --abort
          exit 1
        fi
      done

    - git push origin development
    - echo "Reverse sync completed successfully"
    - echo "Auto-sync will now propagate changes :development → development-second"
  when: manual
  only:
    - development-second
  allow_failure: false
