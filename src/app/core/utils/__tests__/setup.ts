// src/app/core/utils/__tests__/setup.ts
// Performance optimization setup for core/utils tests

import { customMatchers } from './test-helpers';

// Extend Jest expect with custom matchers
expect.extend(customMatchers);

// Performance optimizations
beforeAll(() => {
  // Mock heavy operations
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

// Global test timeout
jest.setTimeout(10000);

// Optimize test isolation
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  
  // Reset DOM
  document.body.innerHTML = '';
  
  // Reset global objects
  if (global.URL) {
    global.URL.createObjectURL = jest.fn();
  }
  
  if (global.navigator) {
    Object.assign(global.navigator, {
      clipboard: undefined
    });
  }
});

afterEach(() => {
  // Clean up after each test
  jest.restoreAllMocks();
});

// Performance monitoring
const performanceMetrics = {
  slowTests: [] as Array<{ name: string; duration: number }>,
  startTime: 0
};

// Track slow tests
beforeEach(() => {
  performanceMetrics.startTime = Date.now();
});

afterEach(() => {
  const duration = Date.now() - performanceMetrics.startTime;
  if (duration > 1000) { // Tests taking more than 1 second
    const testName = expect.getState().currentTestName || 'Unknown test';
    performanceMetrics.slowTests.push({ name: testName, duration });
  }
});

// Report slow tests after all tests complete
afterAll(() => {
  if (performanceMetrics.slowTests.length > 0) {
    // eslint-disable-next-line no-console
    console.warn('\n🐌 Slow tests detected:');
    performanceMetrics.slowTests
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5) // Top 5 slowest tests
      .forEach(test => {
        // eslint-disable-next-line no-console
        console.warn(`  - ${test.name}: ${test.duration}ms`);
      });
  }
}); 