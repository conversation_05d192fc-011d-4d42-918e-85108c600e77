<div class="d-flex flex-wrap align-items-start mb-6">
  <div class="col-12 col-lg-4 col-form-label d-flex flex-column">
    <label [class.required]="required">
      {{ label }}
    </label>
    <span *ngIf="labelDescription" class="text-gray-700"
      ><i>{{ labelDescription }}</i></span
    >
  </div>
  <div class="col-12 col-lg-8">
    <div *ngIf="chipsListData && chipsListData.length > 0" class="mb-2">
      <app-chips (selectedChips)="handleDataOutput($event)" [chipsList]="chipsListData" [config]="{ disabled, showLimit: true }" />
    </div>

    <button
      (click)="handleOpenModalForm()"
      [disabled]="disabled"
      [ngClass]="{
        'btn-outline': btnStyleOutline,
        'btn-outline--disabled': btnStyleDisabled
      }"
      class="btn px-4"
      color="primary"
      mat-button
      type="button"
      [style]="disabled ? { filter: 'grayscale(1)' } : null"
    >
      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS" class="svg-icon svg-icon-2"></span>
      <span [ngClass]="{ 'text-gray-300': btnStyleDisabled }" class="text-primary">{{ ctaLabelOpenModalForm }}</span>
    </button>
  </div>
</div>

<app-modal-form #modalForm [modalConfig]="modalFormConfig" [modalOptions]="{ size: 'md' }">
  <div *ngIf="listbox_selectedItems.value.length && showChipsModal" class="mb-4">
    <app-chips-legacy (dataOutput)="handleDataOutput($event)" [chipsList]="listbox_selectedItems.value"></app-chips-legacy>
  </div>

  <ng-container *ngIf="listbox_listItems.value.length; else noDataTpl">
    <div class="multiselect-search mb-4">
      <div class="input-group">
        <input [formControl]="searchControl" [readOnly]="isLoading.value" class="form-control" ngDefaultControl placeholder="Silahkan cari {{ searchLabel }} disini" type="text" />
        <span class="input-group-text">
          <span [inlineSVG]="'./assets/media/icons/ic_search.svg'" class="svg-icon svg-icon-2"></span>
        </span>
      </div>
    </div>
    <ng-container [ngTemplateOutlet]="(isLoading | async) ? loadingTpl : listboxListTpl" />
  </ng-container>

  <ng-template #loadingTpl>
    <div class="d-flex justify-content-center align-items-center">
      <div class="my-7">
        <mat-spinner></mat-spinner>
        <div class="my-5">Loading Data</div>
      </div>
    </div>
  </ng-template>

  <ng-template #listboxListTpl>
    <div *ngIf="useIndeterminateCheckbox" class="form-check">
      <input #checkboxAll (change)="handleCheckAll($event)" class="form-check-input cursor-pointer" id="checkAll" type="checkbox" />
      <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize fw-bolder" for="checkAll">
        {{ labelCheckAll }}
      </label>
    </div>

    <div class="multiselect-list mh-330px min-h-330px overflow-auto mb-4">
      <div *ngFor="let item of listbox_listItems.value | filterBrandVariant : searchControl.value; let last = last" class="form-check my-6 text-capitalize list-box">
        <input
          (change)="handleChangeSelection(item)"
          [(ngModel)]="item.selected"
          [checked]="item['checked']"
          [indeterminate]="item['indeterminate']"
          class="form-check-input cursor-pointer"
          disableRipple
          id="flexCheckDefault-{{ item.brand_id }}"
          type="checkbox"
        />

        <ng-container *ngIf="item['status_value']; else defaultLabel">
          <div class="d-flex align-items-center">
            <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ item.brand_id }}">
              {{ item.brand_name | lowercase }}
            </label>
            <span class="ms-4 badge badge-bg badge__status badge__status--{{ item['status'] }}">
              {{ item['status_value'] }}
            </span>
          </div>
        </ng-container>

        <ng-container *ngIf="item['variants'].length > 0">
          <div *ngFor="let variant of item['variants']" class="form-check my-6 text-capitalize">
            <input
              (change)="handleChangeSelectionVariant(item, variant)"
              [(ngModel)]="variant.selected"
              class="form-check-input cursor-pointer"
              id="flexCheckDefault-{{ variant.id }}"
              type="checkbox"
            />
            <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ variant.id }}">
              {{ variant.variant_name ?? '-' | lowercase }}
            </label>
          </div>
        </ng-container>

        <ng-template #defaultLabel>
          <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ item.brand_id }}">
            {{ item.brand_name | lowercase }}
          </label>
        </ng-template>

        <span *ngIf="haveDescription">
          <br />
          <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ item.brand_id }}">{{
            item[descriptionKey] | lowercase
          }}</label>
        </span>

        <!-- Scroll Observer -->
        <div *ngIf="last" #scrollObserver class="observer"></div>
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <button (click)="handleAddChipsList()" [disabled]="!listbox_selectedItems.value.length" class="btn btn-primary" type="button">
        {{ ctaLabelAddItems }}
      </button>
    </div>
  </ng-template>

  <ng-template #noDataTpl>
    <p class="px-7">Tidak ada data...</p>
  </ng-template>
</app-modal-form>
