#!/bin/bash

# Branch Synchronization Script
# Usage: ./sync-branches.sh [--mode=dev-only|full] [--dry-run]

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default values
MODE="dev-only"
DRY_RUN=false
CURRENT_BRANCH=$(git branch --show-current)

# Parse arguments
for arg in "$@"; do
    case $arg in
        --mode=*)
            MODE="${arg#*=}"
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--mode=dev-only|full] [--dry-run]"
            echo ""
            echo "Modes:"
            echo "  dev-only  Sync development → development-second only (default)"
            echo "  staging   Sync development → development-second → staging"
            echo "  full      Sync all branches including main (PRODUCTION)"
            echo ""
            echo "Options:"
            echo "  --dry-run Show what would be done without executing"
            echo "  --help    Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $arg"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Validation
if [[ "$MODE" != "dev-only" && "$MODE" != "staging" && "$MODE" != "full" ]]; then
    echo -e "${RED}❌ Invalid mode: $MODE${NC}"
    echo "Valid modes: dev-only, staging, full"
    exit 1
fi

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_git_status() {
    if [[ -n $(git status --porcelain) ]]; then
        log_error "Working directory is not clean. Please commit or stash changes."
        git status --short
        exit 1
    fi
}

sync_branch() {
    local source_branch=$1
    local target_branch=$2
    
    log_info "Syncing $source_branch → $target_branch"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_warning "DRY RUN: Would sync $source_branch → $target_branch"
        return 0
    fi
    
    # Fetch latest changes
    git fetch origin
    
    # Checkout target branch
    git checkout "$target_branch"
    
    # Merge source branch
    git merge "origin/$source_branch" --no-ff -m "CHORE: sync $source_branch into $target_branch"
    
    # Push changes
    git push origin "$target_branch"
    
    log_success "Successfully synced $source_branch → $target_branch"
}

# Main execution
main() {
    log_info "Starting branch synchronization (mode: $MODE)"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_warning "DRY RUN MODE - No actual changes will be made"
    fi
    
    # Check git status
    check_git_status
    
    # Ensure we're on development branch
    if [[ "$CURRENT_BRANCH" != "development" ]]; then
        log_warning "Not on development branch. Switching to development..."
        if [[ "$DRY_RUN" == "false" ]]; then
            git checkout development
        fi
    fi
    
    # Phase 1: Development-second sync (always)
    log_info "Phase 1: Development-second synchronization"
    sync_branch "development" "development-second"

    # Phase 2: Staging sync (if staging or full mode)
    if [[ "$MODE" == "staging" || "$MODE" == "full" ]]; then
        log_info "Phase 2: Staging synchronization"
        log_warning "This will trigger Vercel preview deployment for staging!"

        if [[ "$DRY_RUN" == "false" ]]; then
            read -p "Continue with staging sync? [y/N]: " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                sync_branch "development" "staging"
                log_success "✅ Staging sync completed!"
            else
                log_info "Staging sync cancelled by user"
                return 0
            fi
        else
            log_warning "DRY RUN: Would sync development → staging"
        fi
    fi
    
    # Phase 3: Production sync (if full mode)
    if [[ "$MODE" == "full" ]]; then
        log_warning "Phase 3: PRODUCTION synchronization"
        log_warning "This will trigger Vercel production deployment!"
        
        if [[ "$DRY_RUN" == "false" ]]; then
            read -p "Are you sure you want to sync to main (PRODUCTION)? [y/N]: " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                sync_branch "staging" "main"
                log_success "🎉 Production sync completed!"
                log_warning "🚀 Vercel production deployment will be triggered"
            else
                log_info "Production sync cancelled by user"
            fi
        else
            log_warning "DRY RUN: Would sync staging → main (PRODUCTION)"
        fi
    fi
    
    # Return to original branch
    if [[ "$DRY_RUN" == "false" && "$CURRENT_BRANCH" != "development" ]]; then
        git checkout "$CURRENT_BRANCH"
    fi
    
    log_success "Branch synchronization completed!"
    
    # Usage hints
    case "$MODE" in
        "dev-only")
            log_info "💡 To sync to staging: $0 --mode=staging"
            log_info "💡 To sync to production: $0 --mode=full"
            ;;
        "staging")
            log_info "💡 To sync to production: $0 --mode=full"
            ;;
        "full")
            log_info "🎉 All branches synchronized!"
            ;;
    esac
}

# Execute main function
main "$@"
