import { Component, Input } from '@angular/core';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-noteview-revision',
  standalone: true,
  imports: [InlineSVGModule],
  templateUrl: './noteview-revision.component.html',
  styleUrl: './noteview-revision.component.scss',
})
export class NoteviewRevisionComponent {
  @Input() title: string = 'Catatan Perbaikan';
  @Input() note: string = 'Seharusnya Sales Diskon Mengikuti Sales Discount, bukan tanpa Sales Discount';

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
