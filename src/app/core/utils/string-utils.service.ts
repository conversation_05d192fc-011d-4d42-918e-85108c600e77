import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class StringUtilsService {
  constructor() {}

  arrayStringJoin(arr: any[], key: string | null, separator: string = ',', spaceBefore = false): string {
    if (!arr) return '';

    const space = spaceBefore ? ' ' : '';
    const list = key ? arr.map((item) => item[key]) : arr;
    return list.join(`${separator}${space}`);
  }

  toCapitalize(text: string): string {
    if (!text) return '';
    return text
      .split(' ')
      .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
      .join(' ');
  }

  switchDotAndComma(str: string): string {
    if (!str) return '0';
    const switcher = (match: string) => (match === ',' ? '.' : ',');
    return str.replaceAll(/\.|,/g, switcher);
  }

  switchDotToComma(str: string): string {
    return str.replaceAll('.', ',');
  }

  switchCommaToDot(str: string): string {
    return str.replaceAll(',', '.');
  }

  filterStringArray(array: string[], filter: string): string[] {
    return array.filter((item) => item.includes(filter));
  }

  getTypeDoc(doc: string): 'FILE' | 'IMAGE' | undefined {
    if (!doc) return undefined;
    const match = doc.match(/\/(FILE|IMAGE)_/i);
    return match ? (match[1].toUpperCase() as 'FILE' | 'IMAGE') : undefined;
  }
}
