# Example: Linting Pipeline Configuration
# Rename to linting.yml and uncomment include in main .gitlab-ci.yml when ready

# TypeScript Compilation Check
typescript-check:
  stage: lint
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🔍 Checking TypeScript compilation..."
    - npx tsc --noEmit
    - echo "✅ TypeScript compilation successful"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - merge_requests
    - development
    - staging
    - main

# ESLint Code Quality
eslint-check:
  stage: lint
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🔍 Running ESLint..."
    - npm run lint
    - echo "✅ ESLint checks passed"
  artifacts:
    reports:
      codequality: eslint-report.json
    expire_in: 1 week
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - merge_requests
    - development
    - staging
    - main

# Prettier Formatting Check
prettier-check:
  stage: lint
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🎨 Checking code formatting..."
    - npx prettier --check "src/**/*.{ts,html,css,scss}"
    - echo "✅ Code formatting is correct"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - merge_requests
  allow_failure: true

# Commitlint Check (Enhanced)
commitlint-check:
  stage: lint
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "📝 Checking commit message format..."
    - npx commitlint --from HEAD~1 --to HEAD --verbose
    - echo "✅ Commit message format is correct"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - merge_requests
  allow_failure: true

# Angular Build Check
angular-build-check:
  stage: lint
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🏗️ Testing Angular build..."
    - npm run build
    - echo "✅ Angular build successful"
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - merge_requests
    - development

# Lint Summary
lint-summary:
  stage: lint
  image: alpine:latest
  script:
    - echo "📋 Linting Summary"
    - echo "✅ TypeScript compilation passed"
    - echo "✅ ESLint checks passed"
    - echo "✅ Prettier formatting correct"
    - echo "✅ Commit message format correct"
    - echo "✅ Angular build successful"
  needs:
    - typescript-check
    - eslint-check
    - prettier-check
    - commitlint-check
    - angular-build-check
  when: always
