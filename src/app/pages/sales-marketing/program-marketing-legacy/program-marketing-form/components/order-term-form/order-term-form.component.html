<h3 class="mb-10 fw-bold">Ketentu<PERSON></h3>
<!--<pre>{{ form.value | json }}</pre>-->
<!--<pre>form.valid: {{ form.valid }}</pre>-->
<ng-container *ngIf="(isLoadingSubject | async) === false; else loadingState">
  <!--  <app-note-view-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? ''" [title]="'Catatan Perbaikan'" />-->
  <app-noteview-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? ''" />
  <div [formGroup]="form" class="form-group">
    <div class="w-100 align-items-center mb-4">
      <!--Aturan Pembelian 1 PO-->
      <div class="d-flex flex-wrap my-4">
        <label [class.required]="true" class="col-form-label col-12 col-lg-4"> Aturan Pembelian 1 PO </label>
        <div class="col-12 col-lg-8">
          <div class="form-check text-capitalize p-0 ms-n5">
            <mat-radio-group [formControlName]="'purchase_order_type'" class="w-100 d-flex flex-column" ngDefaultControl>
              <ng-container *ngFor="let option of optionRadioPurchase" class="w-100">
                <mat-radio-button (click)="typePOClick(option)" [disabled]="!isDisableForm" [value]="option.value" class="mb-4">
                  <div class="d-flex flex-column">
                    {{ option.display }}
                    <span class="text-gray-700 mt-1">{{ option.description }}</span>
                  </div>
                </mat-radio-button>
              </ng-container>
            </mat-radio-group>
          </div>
        </div>
      </div>

      <!--Produk-->
      <div class="d-flex flex-wrap my-4">
        <div class="col-12 col-lg-12">
          <!--          <pre>productForm: {{ productForm.value | json }}</pre>-->
          <!--          <fieldset [disabled]="!isDisableForm">-->
          <app-input-list-box-group
            [UrlEndpoint]="ENDPOINT_PRODUCT_GROUP"
            [chipsListData]="valueProduct"
            [disabled]="!isDisableForm || purchaseOrderTypeForm.value === ''"
            [formControlName]="'product'"
            [required]="true"
            [showChipsModal]="false"
            [resetSelected]="shouldResetListBox"
            [btnStyleOutline]="true"
            ctaLabelAddItems="Tambahkan"
            ctaLabelOpenModalForm="Tambah Produk"
            label="Produk Dibeli"
            modalConfirmTitle="Tambah Produk"
            ngDefaultControl
            searchLabel="Pilih Produk"
            title="Pilih Produk"
            (selectionOutput)="productSelection($event)"
          />
          <!--          </fieldset>-->
        </div>
      </div>

      <div *ngIf="purchaseOrderTypeForm.value === EnumProgramMarketingPOType.BUNDLING" [formGroup]="bundlingForm">
        <div *ngFor="let variant of variantForm.controls; index as i" [formArrayName]="'variants'" class="my-4">
          <div [formGroupName]="i" class="d-flex flex-wrap my-4">
            <label [class.required]="true" class="col-form-label col-12 col-lg-4">
              {{ variant.get('variant_name')?.value || variant.get('name')?.value }}
            </label>
            <div class="col-12 col-lg-8 d-flex">
              <div class="flex-column">
                <div class="font-12 mb-4">QTY</div>
                <div class="input-group input-group-solid me-4">
                  <input
                    (input)="changeQtyVariant($event, i, variant.get('delivery_ratio')?.value)"
                    [formControlName]="'qty'"
                    [readOnly]="!isDisableForm"
                    [required]="true"
                    appNumberInput
                    class="form-control form-control-solid"
                    ngDefaultControl
                    placeholder="Input minimal berat/volume"
                  />
                  <span class="input-group-text px-6 text-capitalize ms-n5 fs-12 text-gray-700">box</span>
                </div>
              </div>
              <div class="flex-column ms-4">
                <div class="font-12 mb-4">Volume/Berat</div>
                <div class="input-group input-group-solid">
                  <input
                    [formControlName]="'weight'"
                    [required]="true"
                    class="form-control form-control-solid bg-gray-700"
                    ngDefaultControl
                    placeholder="Input minimal berat/volume"
                    readonly
                  />
                  <span class="input-group-text px-6 text-capitalize ms-n5 fs-12 text-gray-700">{{ variant.get('delivery_unit')?.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="purchaseOrderTypeForm.value === EnumProgramMarketingPOType.ACCUMULATION" [formGroup]="accumulationForm" class="d-flex flex-wrap my-4">
        <label [class.required]="true" class="col-form-label col-12 col-lg-4"> Minimal Pembelian </label>
        <div class="col-12 col-lg-8">
          <div class="form-check text-capitalize p-0 ms-n5">
            <mat-radio-group [formControlName]="'accumulation_type'" class="w-100 d-lg-flex gap-4" ngDefaultControl>
              <ng-container *ngFor="let option of optionRadioMinimalPurchase; index as i" class="w-100">
                <mat-radio-button [disabled]="!isDisableForm || option.disable || !productForm.value" [value]="option.value" class="col-4 {{ option.disable && 'text-gray-700' }}">
                  {{ option.display }}
                </mat-radio-button>
              </ng-container>
            </mat-radio-group>
          </div>

          <div>
            <!--Minimum berat/volume-->
            <div *ngIf="accumulationTypeEnumForm.value === EnumProgramMarketingAccumulationType.MINIMUM_WEIGHT_OR_VOLUME" class="my-4">
              <div class="input-group input-group-solid">
                <input
                  [formControlName]="'minimum_weight_or_volume'"
                  [readOnly]="!isDisableForm"
                  [required]="true"
                  appNumberInputQty
                  class="form-control form-control-solid ms-n5"
                  ngDefaultControl
                  placeholder="Input minimal berat/volume"
                />
                <span class="input-group-text px-6 text-capitalize ms-n5 fs-12 text-gray-700">{{ displayMinimumWeight() }}</span>
              </div>
            </div>

            <!--Minimum qty-->
            <div *ngIf="accumulationTypeEnumForm.value === EnumProgramMarketingAccumulationType.MINIMUM_QTY" class="my-4">
              <div class="input-group input-group-solid">
                <input
                  [formControlName]="'minimum_qty'"
                  [readOnly]="!isDisableForm"
                  [required]="true"
                  appNumberInputQty
                  class="form-control form-control-solid ms-n5"
                  ngDefaultControl
                  placeholder="Input minimal jumlah (QTY)"
                />
                <span class="input-group-text px-6 text-capitalize ms-n5 fs-12 text-gray-700">box</span>
              </div>
            </div>

            <!--Minimum nominal-->
            <div *ngIf="accumulationTypeEnumForm.value === EnumProgramMarketingAccumulationType.MINIMUM_PRICE" class="my-4">
              <div class="input-group input-group-solid">
                <span class="input-group-text px-6 text-capitalize ms-n5 fs-12 text-gray-700">Rp</span>
                <input
                  [formControlName]="'minimum_price'"
                  [readOnly]="!isDisableForm"
                  [required]="true"
                  appNumberInputQty
                  class="form-control form-control-solid border-none input-qty"
                  ngDefaultControl
                  placeholder="Input minimal nominal"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loadingState>
  <div class="d-flex flex-column justify-content-center align-items-center my-5">
    <mat-spinner></mat-spinner>
    <div class="my-4">Loading Form</div>
  </div>
</ng-template>
