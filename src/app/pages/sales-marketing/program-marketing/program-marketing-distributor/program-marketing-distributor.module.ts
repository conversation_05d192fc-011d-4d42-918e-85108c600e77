import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProgramMarketingDistributorComponent } from './program-marketing-distributor.component';
import { DetailMarketingProgramDistributorComponent } from './detail-marketing-program-distributor/detail-marketing-program-distributor.component';
import { ComponentsModule } from '@shared/components/components.module';
import { CardHeaderDetailProgramComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-header-detail-program/card-header-detail-program.component';
import { CardNoteCorrectionProgramComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-note-correction-program/card-note-correction-program.component';
import { CardInformationProgramComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-information-program/card-information-program.component';
import { CardTermProgramComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-term-program/card-term-program.component';
import { CardOrderTermComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-order-term/card-order-term.component';
import { MatButtonModule } from '@angular/material/button';
import { SectionVerifiedBadgeComponent } from '@shared/components/section-verified-badge/section-verified-badge.component';
import { MatRadioModule } from '@angular/material/radio';
import { NoteViewRevisionComponent } from '../../program-marketing-legacy/program-marketing-form/components/note-view-revision/note-view-revision.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ProductsModule } from '@pages/products/products.module';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { DirectiveModule } from '@directives/directive.module';
import { FilterListProgramComponent } from '../../program-marketing-legacy/components/filter-list-program/filter-list-program.component';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { ContentDetailProgramComponent } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/detail-marketing-program-distributor/components/content-detail-program/content-detail-program.component';
import { PoComponentModule } from '@pages/purchase-order/components/po-component.module';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ContentListPOComponent } from '../../program-marketing-legacy/detail-program-marketing/components/content-list-po/content-list-po.component';
import { DistributorProgramFormComponent } from './distributor-program-form/distributor-program-form.component';
import { ModalProgramSelectorComponent } from '../../program-marketing-legacy/components/modal-program-selector/modal-program-selector.component';
import { ButtonAddProgramComponent } from '../../program-marketing-legacy/components/button-add-program/button-add-program.component';
import { ProgramInformationFormComponent } from '../../program-marketing-legacy/program-marketing-form/components/program-information-form/program-information-form.component';
import { ProgramTermFormComponent } from '../../program-marketing-legacy/program-marketing-form/components/program-term-form/program-term-form.component';
import { OrderTermFormComponent } from '../../program-marketing-legacy/program-marketing-form/components/order-term-form/order-term-form.component';
import { RewardTermFormComponent } from '../../program-marketing-legacy/program-marketing-form/components/reward-term-form/reward-term-form.component';
import { DiscountTermFormComponent } from '../../program-marketing-legacy/program-marketing-form/components/discount-term-form/discount-term-form.component';
import { CompensationTermFormComponent } from '../../program-marketing-legacy/program-marketing-form/components/compensation-term-form/compensation-term-form.component';
import { InputSelectDiscountProgramComponent } from '../../program-marketing-legacy/program-marketing-form/components/input-select-discount-program/input-select-discount-program.component';
import { InputSelectDiscountCategoryComponent } from '../../program-marketing-legacy/program-marketing-form/components/input-select-discount-category/input-select-discount-category.component';
import { InputSelectDiscountTypeComponent } from '../../program-marketing-legacy/program-marketing-form/components/input-select-discount-type/input-select-discount-type.component';
import { InputListBoxDistributorLegacyComponent } from '../../program-marketing-legacy/program-marketing-form/components/input-list-box-distributor-legacy/input-list-box-distributor-legacy.component';
import { DiscountTermAddProductsComponent } from '../../program-marketing-legacy/program-marketing-form/components/discount-term-add-products/discount-term-add-products.component';
import { ModalWarehouseSelectorComponent } from '../../program-marketing-legacy/components/modal-warehouse-selector/modal-warehouse-selector.component';
import { CardDiscountTermComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-discount-term/card-discount-term.component';
import { CardRewardTermComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-reward-term/card-reward-term.component';
import { CardInformationCompensationComponent } from '../../program-marketing-legacy/detail-program-marketing/components/card-information-compensation/card-information-compensation.component';
import { ContentListSpmComponent } from '../../program-marketing-legacy/detail-program-marketing/components/content-list-spm/content-list-spm.component';
import { CreateSpmProgramMarketingComponent } from '../../program-marketing-legacy/create-spm-program-marketing/create-spm-program-marketing.component';
import { SpmProductOrderComponent } from '../../program-marketing-legacy/create-spm-program-marketing/spm-product-order/spm-product-order.component';
import { InputListBoxGroupComponent } from '../../program-marketing-legacy/program-marketing-form/components/input-list-box-group/input-list-box-group.component';
import { InputListBoxPromagComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/input-list-box-promag/input-list-box-promag.component';
import { ModalConfirmationProgramMarketingComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/modal-confirmation-program-marketing/modal-confirmation-program-marketing.component';
import { ModalExtendPeriodComponent } from '@pages/sales-marketing/program-marketing-legacy/components/modal-extend-period/modal-extend-period.component';
import { InputSelectOrderUnitTypeComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/input-select-order-unit-type/input-select-order-unit-type.component';
import { InputSelectDiscountSettingComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/input-select-discount-setting/input-select-discount-setting.component';
import { ContentProgramTermComponent } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/detail-marketing-program-distributor/components/content-detail-program/content-program-term/content-program-term.component';
import { CardSectionLoaderComponent } from '@shared/components/loader/card-section-loader/card-section-loader.component';
import { CardSectionDetailComponent } from '@pages/sales-marketing/program-marketing/shared/components/card-section-detail/card-section-detail.component';
import { PurchaseOrderTermFormComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/purchase-order-term-form/purchase-order-term-form.component';
import { SettingRewardPurchaseComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/setting-reward-purchase/setting-reward-purchase.component';
import { CardFormOrderAndDiscountTermComponent } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/distributor-program-form/form/card-form-order-and-discount-term/card-form-order-and-discount-term.component';
import { NoteviewRevisionComponent } from '@pages/sales-marketing/program-marketing/shared/components/noteview-revision/noteview-revision.component';

@NgModule({
  declarations: [
    NoteViewRevisionComponent,
    ProgramMarketingDistributorComponent,
    DetailMarketingProgramDistributorComponent,
    CardHeaderDetailProgramComponent,
    CardNoteCorrectionProgramComponent,
    CardInformationProgramComponent,
    CardTermProgramComponent,
    CardOrderTermComponent,
    FilterListProgramComponent,
    ContentDetailProgramComponent,
    ContentListPOComponent,
    DistributorProgramFormComponent,
    ModalProgramSelectorComponent,
    ModalExtendPeriodComponent,
    ButtonAddProgramComponent,
    ProgramInformationFormComponent,
    ProgramTermFormComponent,
    OrderTermFormComponent,
    RewardTermFormComponent,
    DiscountTermFormComponent,
    CompensationTermFormComponent,
    InputSelectDiscountProgramComponent,
    InputSelectDiscountCategoryComponent,
    InputSelectDiscountTypeComponent,
    DiscountTermAddProductsComponent,
    InputListBoxDistributorLegacyComponent,
    ModalWarehouseSelectorComponent,
    CardDiscountTermComponent,
    CardRewardTermComponent,
    CardInformationCompensationComponent,
    ContentListSpmComponent,
    CreateSpmProgramMarketingComponent,
    SpmProductOrderComponent,
    InputListBoxPromagComponent,
    InputListBoxGroupComponent,
    ModalConfirmationProgramMarketingComponent,
    InputSelectOrderUnitTypeComponent,
    InputSelectDiscountSettingComponent,
    PurchaseOrderTermFormComponent,
    SettingRewardPurchaseComponent,
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    FormsModule,
    InlineSVGModule,
    ComponentsModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatInputModule,
    MatCheckboxModule,
    ProductsModule,
    MatDatepickerModule,
    MatIconModule,
    NgbPopoverModule,
    DirectiveModule,
    MatSortModule,
    MatTableModule,
    MatChipsModule,
    PoComponentModule,
    MatProgressSpinnerModule,
    ContentProgramTermComponent,
    CardSectionLoaderComponent,
    CardSectionDetailComponent,
    CardFormOrderAndDiscountTermComponent,
    NoteviewRevisionComponent,
  ],
  exports: [
    SectionVerifiedBadgeComponent,
    NoteViewRevisionComponent,
    CardHeaderDetailProgramComponent,
    InputListBoxDistributorLegacyComponent,
    CardNoteCorrectionProgramComponent,
    CardSectionDetailComponent,
    ProgramInformationFormComponent,
    ProgramTermFormComponent,
  ],
})
export class ProgramMarketingDistributorModule {}
