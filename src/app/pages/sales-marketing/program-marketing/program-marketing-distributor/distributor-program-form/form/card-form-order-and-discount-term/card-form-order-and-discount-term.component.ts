import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { BehaviorSubject } from 'rxjs';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from '@shared/components/components.module';
import { NoteviewRevisionComponent } from '@pages/sales-marketing/program-marketing/shared/components/noteview-revision/noteview-revision.component';
import { FormBuilder, FormControl } from '@angular/forms';

@Component({
  selector: 'app-card-form-order-and-discount-term',
  standalone: true,
  imports: [CommonModule, ComponentsModule, NoteviewRevisionComponent],
  templateUrl: './card-form-order-and-discount-term.component.html',
  styleUrl: './card-form-order-and-discount-term.component.scss',
})
export class CardFormOrderAndDiscountTermComponent extends BaseComponent implements OnInit {
  isLoadingSubject = new BehaviorSubject(false);

  form: FormGroup = this.fb.group({
    purchase_order_type_enum: new FormControl(null, [Validators.required]),
    minimum_purchase_order_enum: new FormControl(null, [Validators.required]),
  });

  constructor(private fb: FormBuilder) {
    super();
  }

  ngOnInit(): void {
    this.isLoadingSubject.next(true);
    setTimeout(() => {
      this.isLoadingSubject.next(false);
    }, 1000);
  }
}
