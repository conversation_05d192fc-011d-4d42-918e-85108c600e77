// src/app/core/utils/__tests__/test-data.ts
// Centralized test data for core/utils tests

export const TEST_DATA = {
  // User data for various tests
  users: [
    { id: 1, name: '<PERSON>', age: 30, email: '<EMAIL>' },
    { id: 2, name: '<PERSON>', age: 25, email: '<EMAIL>' },
    { id: 3, name: '<PERSON>', age: 35, email: '<EMAIL>' }
  ],

  // Number data for number utils tests
  numbers: {
    integers: [0, 1000, 10000, 100000, 1234567],
    decimals: [0.5, 1234.56, 9999.99, 0.01],
    currencies: [1000, 15000, 250000, 1000000],
    strings: ['1000', '15000', '250000', '1000000']
  },

  // Date data for date utils tests
  dates: {
    iso: ['2024-01-01', '2024-12-31', '2023-06-15'],
    formatted: ['01-01-2024', '31-12-2024', '15-06-2023'],
    epochs: [1704067200000, 1735689600000, 1686787200000] // Corresponding to iso dates
  },

  // String data for string utils tests
  strings: {
    simple: ['hello', 'world', 'test'],
    withSpaces: ['hello world', 'test string', 'multiple words'],
    numbers: ['1,23.456', '1.23,456', '1000.50'],
    urls: [
      'https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/FILE_92364ae1',
      'https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_92364ae1',
      'https://example.com/user/DOCUMENT_abc'
    ]
  },

  // Array data for array operations
  arrays: {
    simple: ['A', 'B', 'C', 'D'],
    objects: [
      { key: 'name', value: 'John' },
      { key: 'age', value: 30 },
      { key: 'city', value: 'Jakarta' }
    ],
    mixed: ['text', 123, true, null, undefined]
  },

  // Object data for object utils tests
  objects: {
    simple: { a: 1, b: 2, c: 3 },
    nested: { user: { name: 'John', age: 30 }, active: true },
    empty: {},
    withNulls: { a: '', b: null, c: undefined, d: false, e: 0 },
    withValues: { a: '', b: null, c: undefined, d: false, e: 0, f: 'value' }
  },

  // Browser user agents for DOM utils tests
  browsers: [
    {
      name: 'chrome',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/90.0'
    },
    {
      name: 'firefox',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Firefox/88.0'
    },
    {
      name: 'safari',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/537.36'
    },
    {
      name: 'ie',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Trident/7.0'
    },
    {
      name: 'opera',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) OPR/75.0'
    },
    {
      name: 'edge',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Edg/18.0'
    },
    {
      name: 'other',
      userAgent: 'Some unknown agent'
    }
  ],

  // CSV data for file utils tests
  csv: {
    simple: 'name,age\nJohn,30\nJane,25',
    withQuotes: '"name","age"\n"John Doe","30"\n"Jane Smith","25"',
    empty: '',
    headersOnly: 'name,age,email'
  },

  // Card data for data diff utils tests
  cards: {
    simple: [
      { key: 'name', label: 'Name', value: 'John' },
      { key: 'age', label: 'Age', value: 30 }
    ],
    withArrays: [
      { key: ['first', 'last'], label: 'Name', value: 'John, Doe' },
      { key: 'status', label: 'Status', value: 'active' }
    ]
  }
};

// Helper functions for test data
export const TestDataHelpers = {
  // Generate random test data
  randomUser: () => ({
    id: Math.floor(Math.random() * 1000),
    name: `User ${Math.random().toString(36).substr(2, 5)}`,
    age: Math.floor(Math.random() * 50) + 18,
    email: `user${Math.random().toString(36).substr(2, 5)}@example.com`
  }),

  // Generate array of random users
  randomUsers: (count: number) => 
    Array.from({ length: count }, () => TestDataHelpers.randomUser()),

  // Generate random number
  randomNumber: (min: number = 0, max: number = 1000000) =>
    Math.floor(Math.random() * (max - min + 1)) + min,

  // Generate random date
  randomDate: () => {
    const start = new Date(2020, 0, 1);
    const end = new Date(2025, 11, 31);
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  }
}; 