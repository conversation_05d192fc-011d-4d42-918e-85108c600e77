import { NumberUtilsService } from '../number-utils.service';

describe('NumberUtilsService', () => {
  let service: NumberUtilsService;

  beforeEach(() => {
    service = new NumberUtilsService();
  });

  describe('toRupiah', () => {
    it('should format number to Rupiah with symbol', () => {
      expect(service.toRupiah(10000)).toBe('Rp10.000,00');
    });

    it('should format number to Rupiah without symbol', () => {
      expect(service.toRupiah(10000, false, false)).toBe('10.000,00');
    });

    it('should truncate decimal if flag set', () => {
      expect(service.toRupiah(12345.67, true)).toBe('Rp12.345,00');
    });

    it('should return 0 if value is undefined', () => {
      expect(service.toRupiah(undefined)).toBe('Rp0,00');
    });
  });

  describe('convertTwoDigits', () => {
    it('should convert number to 2 decimal digits', () => {
      expect(service.convertTwoDigits(123)).toBe('123.00');
    });
  });

  describe('toThousandConvert', () => {
    it('should format string number with thousand separator', () => {
      expect(service.toThousandConvert('12345.678')).toBe('12.345,68');
    });

    it('should format numeric value with thousand separator', () => {
      expect(service.toThousandConvert(12345.678)).toBe('12.345,68');
    });
  });

  describe('toStringWithUnitType', () => {
    it('should format number string with unit', () => {
      expect(service.toStringWithUnitType('10000 KG')).toBe('10.000 KG');
    });
  });

  describe('toNumberFormat', () => {
    it('should format number to localized string', () => {
      expect(service.toNumberFormat(15000)).toBe('15.000');
    });
  });

  describe('stringNumberToPayload', () => {
    it('should remove dots and convert to number', () => {
      expect(service.stringNumberToPayload('12.345')).toBe(12345);
    });

    it('should return 0 for empty string', () => {
      expect(service.stringNumberToPayload('')).toBe(0);
    });
  });

  describe('convertStringToNumberWithComma', () => {
    it('should convert comma to dot and parse float', () => {
      expect(service.convertStringToNumberWithComma('12,34')).toBe(12.34);
    });
  });

  describe('formatInternationalNumber', () => {
    it('should normalize thousand and decimal', () => {
      expect(service.formatInternationalNumber('1.234,56')).toBe(1234.56);
    });

    it('should return 0 for empty string', () => {
      expect(service.formatInternationalNumber('')).toBe(0);
    });
  });

  describe('reverseFormatInternationalNumber', () => {
    it('should format number to string with dots and comma', () => {
      expect(service.reverseFormatInternationalNumber(1234.56)).toBe('1.234,56');
    });

    it('should format with fixed decimals if true', () => {
      expect(service.reverseFormatInternationalNumber(1000, true)).toBe('1.000,00');
    });

    it('should return "0" for NaN', () => {
      expect(service.reverseFormatInternationalNumber(NaN)).toBe('0');
    });
  });
});
