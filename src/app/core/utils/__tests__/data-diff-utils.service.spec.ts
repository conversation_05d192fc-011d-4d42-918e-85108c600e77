import { TestBed } from '@angular/core/testing';
import { DataDiffUtilsService } from '../data-diff-utils.service';
import { ICardDetailBody } from '@models/card.model';

describe('DataDiffUtilsService', () => {
  let service: DataDiffUtilsService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(DataDiffUtilsService);
  });

  describe('generateUpdateValue', () => {
    it('should update value when key is string and new value is different', () => {
      const data = [{ key: 'name', label: 'Name', value: '<PERSON>' }];
      const newData = { name: '<PERSON>' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBe('Jane');
    });

    it('should not update value when key is string and value is the same', () => {
      const data = [{ key: 'name', label: 'Name', value: '<PERSON>' }];
      const newData = { name: '<PERSON>' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBeUndefined();
    });

    it('should update value when key is array and new values are different', () => {
      const data = [{ key: ['first', 'last'], label: 'Name', value: 'John, Doe' }];
      const newData = { first: 'Jane', last: 'Doe' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBe('Jane, Doe');
    });

    it('should not update when key is array and new values are the same', () => {
      const data = [{ key: ['first', 'last'], label: 'Name', value: 'John, Doe' }];
      const newData = { first: 'John', last: 'Doe' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBeUndefined();
    });

    it('should skip if no matching key in newData', () => {
      const data = [{ key: 'age', label: 'Age', value: 30 }];
      const newData = {};

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBeUndefined();
    });

    it('should not add "new" when val.value === newData[val.key]', () => {
      const data = [{ key: 'status', label: 'Status', value: 'active' }];
      const newData = { status: 'active' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBeUndefined();
    });

    it('should not add "new" when array key values match existing value', () => {
      const data = [{ key: ['x', 'y'], label: 'Point', value: '1, 2' }];
      const newData = { x: '1', y: '2' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBeUndefined();
    });

    it('should not update when some array keys are missing and fallback results in same value', () => {
      const data = [{ key: ['first', 'last'], label: 'Name', value: 'John, Doe' }];
      const newData = { last: 'Doe' };

      const result = service.generateUpdateValue(data, newData);
      expect(result[0].new).toBeUndefined();
    });
  });

  describe('generateUpdateEditData', () => {
    it('should detect changed value', () => {
      const oldData: ICardDetailBody[] = [{ key: 'name', label: 'Name', value: 'John' }];
      const newData: ICardDetailBody[] = [{ key: 'name', label: 'Name', value: 'Jane' }];

      const result = service.generateUpdateEditData(oldData, newData);
      expect(result[0].new).toBe('Jane');
    });

    it('should not add new when values are the same', () => {
      const oldData: ICardDetailBody[] = [{ key: 'name', label: 'Name', value: 'John' }];
      const newData: ICardDetailBody[] = [{ key: 'name', label: 'Name', value: 'John' }];

      const result = service.generateUpdateEditData(oldData, newData);
      expect(result[0].new).toBeUndefined();
    });

    it('should ignore array type values in generateUpdateEditData', () => {
      // @ts-ignore
      const oldData: ICardDetailBody[] = [{ key: 'name', label: 'Name', value: ['A'] }];
      // @ts-ignore
      const newData: ICardDetailBody[] = [{ key: 'name', label: 'Name', value: ['B'] }];

      const result = service.generateUpdateEditData(oldData, newData);
      expect(result[0].new).toBeUndefined();
    });
  });

  describe('generateSideDocument', () => {
    it('should return current and new if changed', () => {
      const key = 'doc';
      const current = { key: 'current', value: 'old.pdf' };
      const newData = { doc: 'new.pdf' };

      const result = service.generateSideDocument(key, newData, current);
      expect(result).toEqual([
        { key: 'current', value: 'old.pdf' },
        { key: 'new', value: 'new.pdf' },
      ]);
    });

    it('should return only current if no change', () => {
      const key = 'doc';
      const current = { key: 'current', value: 'same.pdf' };
      const newData = { doc: 'same.pdf' };

      const result = service.generateSideDocument(key, newData, current);
      expect(result).toEqual([current]);
    });

    it('should return only current if key not in newData', () => {
      const key = 'doc';
      const current = { key: 'current', value: 'old.pdf' };
      const newData = {};

      const result = service.generateSideDocument(key, newData, current);
      expect(result).toEqual([current]);
    });
  });
});
