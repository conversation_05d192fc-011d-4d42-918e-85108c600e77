{"name": "atom-backoffice-data", "version": "1.14.4", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-dev": "ng build --configuration development", "start-dev": "ng serve --configuration development", "build-dev2": "ng build --configuration development-second", "start-dev2": "ng serve --configuration development-second", "build-staging": "ng build --configuration staging", "start-staging": "ng serve --configuration staging", "rtl": "webpack --config=rtl.config.js", "test": "jest", "test:verbose": "jest --verbose", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:no-watch": "jest --watchAll=false", "test:single": "jest --test<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:name": "jest --testNamePattern", "test:data-diff": "jest --testPathPatterns=\"data-diff-utils.service.spec.ts\"", "test:generate-update": "jest --testNamePattern=\"generateUpdateValue\" --testPathPatterns=\"data-diff-utils.service.spec.ts\"", "prepare": "husky install", "lint": "ng lint", "format": "npx prettier \"src/**/*.{js,ts,html,css,scss}\" --write ", "release": "release-it", "validate-ci": "node scripts/validate-gitlab-ci.js", "build:scripts": "npx tsc scripts/archive-changelog.ts --outDir scripts/dist --target es2020 --module commonjs --moduleResolution node --esModuleInterop --allowSyntheticDefaultImports --skipLibCheck", "archive:changelog": "npm run build:scripts && node scripts/dist/archive-changelog.js", "prerelease": "npm run build:scripts"}, "private": true, "dependencies": {"@angular/animations": "18.2.13", "@angular/cdk": "18.2.14", "@angular/common": "18.2.13", "@angular/compiler": "18.2.13", "@angular/core": "18.2.13", "@angular/forms": "18.2.13", "@angular/google-maps": "18.2.14", "@angular/material": "18.2.14", "@angular/material-moment-adapter": "18.2.14", "@angular/platform-browser": "18.2.13", "@angular/platform-browser-dynamic": "18.2.13", "@angular/router": "18.2.13", "@ng-bootstrap/ng-bootstrap": "17.0.0", "@ngx-translate/core": "14.0.0", "@ngx-translate/http-loader": "7.0.0", "@popperjs/core": "2.11.8", "@sweetalert2/ngx-sweetalert2": "^13.0.0", "@types/google.maps": "^3.55.12", "angular-in-memory-web-api": "^0.18.0", "angular2-qrcode": "^2.0.3", "animate.css": "4.1.1", "bootstrap": "5.2.3", "ckeditor4-angular": "^4", "date-fns": "^2.29.3", "moment": "^2.29.4", "ng-inline-svg-2": "^19.0.0", "ng-select2-component": "^10.0.0", "ngx-clipboard": "15.1.0", "ngx-editor": "^19.0.0-beta.1", "ngx-image-cropper": "^7.2.1", "ngx-infinite-scroll": "15.0.0", "ngx-print-element": "^2.1.8", "ngx-skeleton-loader": "6.0.0", "object-path": "0.11.8", "rxjs": "7.5.5", "sweetalert2": "^11.12.3", "swiper": "^8.4.4", "tslib": "2.4.0", "zone.js": "0.14.10"}, "devDependencies": {"@angular-builders/jest": "^19.0.1", "@angular-devkit/build-angular": "^18.2.20", "@angular-eslint/builder": "18.4.3", "@angular-eslint/eslint-plugin": "^18.4.3", "@angular-eslint/eslint-plugin-template": "^18.4.3", "@angular-eslint/schematics": "18.4.3", "@angular-eslint/template-parser": "18.4.3", "@angular/cli": "18.2.12", "@angular/compiler-cli": "18.2.13", "@angular/localize": "^18.2.13", "@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "@release-it/bumper": "^5.1.0", "@release-it/conventional-changelog": "^7.0.2", "@types/bootstrap": "5.1.12", "@types/jest": "^30.0.0", "@types/node": "17.0.36", "@types/object-path": "0.11.1", "@types/prismjs": "1.26.0", "@types/sass-loader": "8.0.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@typescript-eslint/utils": "^8.21.0", "css-loader": "6.7.1", "del": "6.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "jest-preset-angular": "^14.6.0", "lint-staged": "^13.1.2", "mini-css-extract-plugin": "2.6.1", "prettier": "^2.8.4", "release-it": "^16.2.1", "rtlcss-webpack-plugin": "4.0.7", "sass-loader": "13.0.2", "ts-node": "^10.9.2", "typescript": "5.4.5", "webpack": "^5.93.0", "webpack-cli": "4.10.0"}, "husky": {"hooks": {"pre-commit": "npx lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"src/app/**/*.{ts,html}": "prettier --write", "src/app/**/*.ts": "eslint"}, "resolutions": {"autoprefixer": "10.4.5"}}