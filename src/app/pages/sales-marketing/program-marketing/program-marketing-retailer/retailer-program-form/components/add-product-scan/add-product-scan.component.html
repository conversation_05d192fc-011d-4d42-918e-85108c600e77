<!-- SELECTED CHIPS -->
<div class="mb-2" *ngIf="!!chipsListData.length">
  <!--<pre>chipsListData: {{ chipsListData|json }}</pre>-->
  <!--<pre>listSelectedItems: {{ listSelectedItems | json }}</pre>-->
  <app-chips [chipsList]="chipsListData" (selectedChips)="onSelectedChips($event)" [config]="{ showLimit: true, limit: 4, labelTextStyle: 'uppercase' }" />
</div>

<!--CTA ADD PRODUCT-->
<button
  mat-button
  color="primary"
  class="btn btn-outline btn-outline-secondary px-4"
  [disabled]="disabled"
  (click)="onOpenModal()"
  [ngStyle]="disabled ? { filter: 'grayscale(1)' } : null"
>
  <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS"></span>
  <span class="text-primary ms-2">Tambah Produk</span>
</button>

<!-- MODAL-->
<app-modal-form #modalForm [modalConfig]="modalFormConfig" [modalOptions]="{ size: 'md' }">
  <mat-tab-group class="square" mat-stretch-tabs="true" animationDuration="0" (selectedIndexChange)="onTabChange($event)" [(selectedIndex)]="tabIndex">
    <mat-tab *ngFor="let item of tabList" [label]="item.labelKey">
      <ng-template matTabContent>
        <ng-container [ngTemplateOutlet]="searchBoxTpl" [ngTemplateOutletContext]="{ tab: item.tab }"></ng-container>
        <ng-container [ngTemplateOutlet]="(loadingSubject | async) ? loadingTpl : listItemsTpl"></ng-container>

        <ng-template #listItemsTpl>
          <div class="multiselect-list min-h-200px mh-400px overflow-auto mb-4">
            <ng-container *ngIf="loadingSubject | async" [ngTemplateOutlet]="loadingTpl"></ng-container>
            <ng-container [ngSwitch]="item.tab">
              <div *ngSwitchCase="'product_variant'">
                <ng-container *ngFor="let item of listItemsBank; let last = last; trackBy: trackByItem">
                  <ng-container *ngIf="item.is_variant" [ngTemplateOutlet]="checkBoxTpl" [ngTemplateOutletContext]="{ data: item }"></ng-container>
                </ng-container>
                <ng-container *ngIf="!listItemsBank.length" [ngTemplateOutlet]="noDataTpl"></ng-container>
              </div>
              <div *ngSwitchCase="'product_brand'">
                <ng-container *ngFor="let item of listItemsBank; let last = last; trackBy: trackByItem">
                  <ng-container *ngIf="!item.is_variant" [ngTemplateOutlet]="checkBoxTpl" [ngTemplateOutletContext]="{ data: item }"></ng-container>
                </ng-container>
                <ng-container *ngIf="!listItemsBank.length" [ngTemplateOutlet]="noDataTpl"></ng-container>
              </div>
            </ng-container>

            <ng-template #checkBoxTpl let-data="data">
              <div class="form-check my-6 text-capitalize list-box">
                <input
                  type="checkbox"
                  class="form-check-input cursor-pointer"
                  id="flexCheckDefault--{{ data.id }}"
                  [(ngModel)]="data.selected"
                  [disabled]="data.disabled ?? false"
                  (change)="onToggleItemSelection(data)"
                />
                <label for="flexCheckDefault--{{ data.id }}" class="form-check-label cursor-pointer ms-2 text-gray-900">{{ data.name | lowercase }}</label>
              </div>
            </ng-template>
          </div>
        </ng-template>
      </ng-template>
    </mat-tab>
  </mat-tab-group>

  <div class="d-flex justify-content-end">
    <button [disabled]="!listSelectedItems.length || loadingSubject.value" class="btn btn-primary min-w-150px" mat-raised-button color="primary" (click)="onAddSelectedItems()">
      <div class="d-flex align-items-center">
        <span>Tambahkan</span>
        <span *ngIf="loadingSubject | async" class="spinner-border spinner-border-sm align-middle ms-2"></span>
      </div>
    </button>
  </div>
</app-modal-form>

<ng-template #searchBoxTpl let-tab="tab">
  <div class="multiselect-search mb-6">
    <div class="input-group">
      <input type="text" class="form-control" placeholder="Cari nama produk" [(ngModel)]="searchKeyword" (ngModelChange)="onChangeKeyword(tab, $event)" />
      <span class="input-group-text">
        <span [inlineSVG]="'./assets/media/icons/ic_search.svg'" class="svg-icon svg-icon-2"></span>
      </span>
      <button *ngIf="!!searchKeyword" class="input-group-text bg-transparent border-0 border-start" type="button" (click)="resetSearch()">
        <span class="svg-icon svg-icon-2" [inlineSVG]="STRING_CONSTANTS.ICON.IC_SEARCH_RESET"></span>
      </button>
    </div>
  </div>
</ng-template>

<ng-template #loadingTpl>
  <app-input-listbox-loader />
</ng-template>

<ng-template #noDataTpl>
  <p>Tidak ada data...</p>
</ng-template>
