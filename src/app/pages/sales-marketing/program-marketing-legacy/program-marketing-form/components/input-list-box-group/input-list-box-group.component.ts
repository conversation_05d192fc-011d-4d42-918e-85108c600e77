import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { IChips } from '@shared/components/v1/chips/chips.interface';
import { BehaviorSubject, debounceTime, Observable } from 'rxjs';
import { IEndpointData } from '@pages/products/Products.interface';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { BaseResponse } from '@shared/base/base-response';
import { FormControl, FormControlName, FormGroup, FormGroupDirective } from '@angular/forms';
import { BaseService } from '@services/base-service.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-input-list-box-group',
  templateUrl: './input-list-box-group.component.html',
  styleUrls: ['./input-list-box-group.component.scss'],
})
export class InputListBoxGroupComponent implements OnInit, OnChanges, AfterViewInit {
  @ViewChild('modalForm') private modalFormComponent: ModalComponent;

  @Input() required: boolean = false;
  @Input() label: string;
  @Input() labelDescription: string;
  @Input() chipsListData: IChips[] = [];
  @Input() ctaLabelAddItems: string;
  @Input() ctaLabelOpenModalForm: string;
  @Input() searchLabel: string;
  @Input() modalConfirmTitle: string;
  @Input() isPlantTypeList: boolean = true;
  @Input() UrlEndpoint: string;
  @Input() ListValue: BehaviorSubject<IChips[] | any> = new BehaviorSubject<IChips[] | any>([]);
  @Input() haveDescription: boolean = false;
  @Input() descriptionKey: string = '';
  @Input() showChipsModal: boolean = true;

  @Input() btnStyleOutline: boolean = false;
  @Input() btnStyleDisabled: boolean = false;
  @Input() disabled: boolean = false;

  @Input() initListBoxWitParam: boolean = false;
  @Input() endPointWithParam: IEndpointData;
  // @Input() outputAll = false; // send only id on setFormControlValue;

  @Input() useIndeterminateCheckbox = false; // use check all
  @Input() labelCheckAll = '';

  @ViewChild('checkboxAll') checkboxAll: ElementRef;
  @ViewChild('scrollObserver') scrollObserver!: ElementRef;

  @Input()
  set resetSelected(value: boolean) {
    if (value) {
      this.deleteAllCheckbox();
    }
  }

  @Output() selectionOutput = new EventEmitter();

  modalFormConfig: ModalConfig;
  searchControl: FormControl;
  listbox_listItems: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  listbox_selectedItems: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  STRING_CONSTANTS = STRING_CONSTANTS;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  listbox_listItems$: Observable<BaseResponse<any[]>>;

  valueFormGroup: FormGroup;
  valueFormControl?: FormControl;

  page = 0;
  limit = 10;

  constructor(private baseService: BaseService, private formGroupDirective: FormGroupDirective, private formControlNameDirective: FormControlName, private cdr: ChangeDetectorRef) {
    this.searchControl = new FormControl('');
  }

  ngOnInit(): void {
    this.initModalForm();
    this.initListBox();
    this.changeKeyword();

    this.valueFormGroup = this.formGroupDirective.form;
    this.valueFormControl = this.formGroupDirective.getControl(this.formControlNameDirective);

    if (this.ListValue.value.length) {
      this.handleValueChanges();
    }
  }

  ngAfterViewInit() {
    this.setupObserver();
  }

  setupObserver() {
    if (!this.scrollObserver) return;

    // const lastItem = document.querySelector('.list-box:last-child');
    // console.log('lastItem', lastItem)
    // if (!lastItem) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !this.isLoading.value) {
          this.getListBoxData();
          observer.disconnect(); // Stop observing
        }
      },
      { threshold: 1 }
    );

    setTimeout(() => {
      this.scrollObserver && observer.observe(this.scrollObserver.nativeElement);
    }, 150);
  }

  handleValueChanges() {
    this.valueFormControl?.valueChanges.subscribe((value) => {
      if (value.length > 0) {
        this.ListValue.next(value);
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.listBoxItems && !changes.listBoxItems.firstChange) {
      this.listbox_listItems = changes.listBoxItems.currentValue;
    }

    if (changes.chipsListData && !changes.chipsListData.firstChange && !!changes.chipsListData.currentValue.length) {
      this.chipsListData = changes.chipsListData.currentValue;
      this.setSelectedChipsList();
      this.mapSelectedItems(this.listbox_listItems.value);
    }

    if (changes.endPointWithParam && !changes.endPointWithParam.firstChange) {
      this.endPointWithParam = changes.endPointWithParam.currentValue;
      this.getListBoxDataWithParam(this.endPointWithParam);
    }

    if (changes.modalConfirmTitle && !changes.modalConfirmTitle.firstChange) {
      this.modalConfirmTitle = changes.modalConfirmTitle.currentValue;
      this.initModalForm();
    }
  }

  initModalForm() {
    this.modalFormConfig = {
      modalTitle: this.modalConfirmTitle,
    };
  }

  deleteAllCheckbox(): void {
    const resetListItems = this.listbox_listItems.value.map((item) => {
      item.selected = false;
      item.checked = false;
      item.indeterminate = false;

      if (item.variants && item.variants.length > 0) {
        item.variants.forEach((variant: any) => {
          variant.selected = false;
        });
      }

      return item;
    });

    this.listbox_listItems.next(resetListItems);
    this.listbox_selectedItems.next([]);
    this.chipsListData = [];
    this.getCheckedAll();
  }

  initListBox() {
    return this.initListBoxWitParam ? this.getListBoxDataWithParam(this.endPointWithParam) : this.getListBoxData();
  }

  getListBoxData(keyword?: string) {
    // const _keyword = keyword ? (this.page ? '&keyword=' + keyword : '?keyword=' + keyword) : '';
    // const _page = this.page ? '?page=' + this.page : '';

    const _keyword = keyword ? '?keyword=' + keyword : '';
    if (keyword) this.page = 0;
    const _page = this.page ? '?page=' + this.page : '';
    this.listbox_listItems$ = this.baseService.getData(this.UrlEndpoint + _page + _keyword);
    this.getData();
    this.setSelectedChipsList();
  }

  getListBoxDataWithParam(data: IEndpointData) {
    if (!Object.keys(data).length) {
      return;
    }

    const { api, body } = data;
    this.listbox_listItems$ = this.baseService.postData(api, body);

    this.getData();
    this.setSelectedChipsList();
  }

  getData() {
    this.btnStyleDisabled = true;
    this.listbox_listItems$.subscribe((list) => {
      if (list) {
        let { data } = list;

        if (!data) {
          return;
        }

        const _excludedEmptyName = data.filter((item) => item.brand_name != null && item.brand_name !== '');
        _excludedEmptyName.map((el) => (el.selected = false));

        data = _excludedEmptyName;

        const brandNames = this.listbox_listItems.value.map((item) => item.brand_name);
        data = data.filter((item) => {
          return !brandNames.includes(item.brand_name);
        });

        this.listbox_listItems.next([...this.listbox_listItems.value, ...data]);
        this.mapSelectedItems(this.listbox_listItems.value);

        this.btnStyleDisabled = false;
        this.page++;
        setTimeout(() => {
          if (list.total_data >= 20) {
            this.setupObserver();
          }
        }, 150); // Reconnect after data loads
      }
    });
  }

  mapSelectedItems(listItems: any[]) {
    if (!this.chipsListData.length) return;

    const _list: any[] = [];
    listItems.map((value) => {
      this.chipsListData.map((chips) => {
        value.variants.map((variant: any) => {
          if (variant.id === chips.id) {
            variant.selected = true;
            const sumVariant = value.variants.length;
            const _checkVariantChecked = value.variants?.filter((val: any) => val.selected).length;
            value.indeterminate = _checkVariantChecked < sumVariant;
            value.selected = _checkVariantChecked === sumVariant;
            _list.push(variant);
          }
        });
      });
    });

    this.listbox_selectedItems.next(_list);
  }

  handleChangeSelection(item: any) {
    this.setSelectedItems(item);
  }

  handleChangeSelectionVariant(item: any, variant: any) {
    this.setSelectedItems(item, variant);
  }

  setSelectedItems(item: any, variant?: any) {
    if (variant) {
      const _checkVariantChecked = item.variants?.filter((val: any) => val.selected).length;
      item.indeterminate = item.variants.length > 0;
      if (item.variants.length === _checkVariantChecked) {
        item.indeterminate = false;
        item.checked = true;
      } else if (item.variants.length > 0 && _checkVariantChecked > 0 && item.variants.length !== _checkVariantChecked) {
        item.indeterminate = true;
      } else {
        item.checked = false;
        item.indeterminate = false;
      }
    } else {
      item.variants.map((variant: any) => {
        variant.selected = item.selected;
        return item;
      });
    }
    const _checkSelected = this.listbox_listItems.value.filter((value) => value.selected || value.checked || value.indeterminate);
    this.listbox_selectedItems.next(_checkSelected);
  }

  handleDataOutput(items: IChips[]) {
    const data: any[] = [];
    items.map((item) => {
      const brand = this.listbox_selectedItems.value.find((value: any) => {
        return value.variants?.find((val: any) => val.id === item.id) && value;
      });
      const filterData = data.filter((value) => value.brand_id === brand?.brand_id);
      if (filterData.length > 0) {
        filterData[0].variants.push(item);
      } else {
        data.push({
          ...item,
          variants: [item],
        });
      }
    });

    // map listbox list items for selected items
    this.listbox_selectedItems.next(data);
    this.selectionOutput.emit(this.listbox_selectedItems);

    this.setFormControlValue();
    this.handleRemovedChips();
    if (this.useIndeterminateCheckbox) return this.getCheckedAll();
  }

  handleAddChipsList() {
    this.selectionOutput.emit(this.listbox_selectedItems);
    this.toggleShowSelectedList();

    this.setFormControlValue();
    return this.modalFormComponent.close();
  }

  setFormControlValue() {
    const _arrId: string[] = [];
    this.listbox_selectedItems.subscribe((brands: any) => {
      brands.map((brand: any) => {
        const variants = brand?.variants?.filter((val: any) => val.selected);
        variants?.map((variant: any) => {
          if (variant.selected === true) return _arrId.push(variant);
        });
      });
    });
    this.valueFormControl?.setValue(_arrId);
  }

  toggleShowSelectedList() {
    const variants: IChips[] = [];
    this.listbox_selectedItems.value.map((item) => {
      item.variants
        .filter((variant: any) => variant.selected)
        .map((value: any) => {
          variants.push({
            id: value.id,
            name: value.variant_name,
            selected: true,
            variant_id: value.id,
            ...value,
          });
        });
    });
    this.chipsListData = variants;
  }

  handleOpenModalForm = () => {
    this.modalFormComponent.open().then();
    if (this.chipsListData.length) this.getCheckedAll();
    setTimeout(() => {
      this.setupObserver();
    }, 150);
  };

  setSelectedChipsList() {
    if (!this.chipsListData.length) {
      this.listbox_selectedItems.next(this.chipsListData);
      this.isLoading.next(false);
      return;
    }

    this.chipsListData.map((chips) => (chips.selected = true));
    this.isLoading.next(false);
  }

  handleRemovedChips() {
    const _findRemovedChips = this.chipsListData.filter((chips) => !chips.selected);
    const _listBoxItems = this.listbox_listItems.value;

    _listBoxItems.map((listBoxItem) => {
      _findRemovedChips.map((item) => {
        const _filterVariant = listBoxItem.variants.find((variant: any) => variant.id === item.id);
        if (_filterVariant) {
          _filterVariant.selected = item.selected;

          const _checkVariantChecked = listBoxItem.variants?.filter((val: any) => val.selected).length;
          if (listBoxItem.variants.length === _checkVariantChecked) {
            listBoxItem.indeterminate = false;
            listBoxItem.checked = true;
          } else if (listBoxItem.variants.length > 0 && _checkVariantChecked > 0 && listBoxItem.variants.length !== _checkVariantChecked) {
            listBoxItem.indeterminate = true;
          } else {
            listBoxItem.checked = false;
            listBoxItem.indeterminate = false;
          }
        }
      });
    });

    this.listbox_listItems.next(_listBoxItems);
  }

  getCheckedAll() {
    if (!this.useIndeterminateCheckbox) return;
    const selectedItemsLength = this.listbox_selectedItems.value.length;
    const listItemsLength = this.listbox_listItems.value.length;

    const isCheckedAll = selectedItemsLength === listItemsLength;

    let checkedValue = false;
    let indeterminateValue = false;

    if (!selectedItemsLength) {
      checkedValue = false;
      indeterminateValue = false;
    }

    if (selectedItemsLength > 0 && selectedItemsLength < listItemsLength) {
      checkedValue = false;
      indeterminateValue = true;
    }

    if (isCheckedAll) {
      checkedValue = true;
      indeterminateValue = false;
    }

    this.checkboxAll.nativeElement.checked = checkedValue;
    this.checkboxAll.nativeElement.indeterminate = indeterminateValue;
  }

  handleCheckAll(e: any) {
    const selectedValue = e.target.checked;
    this.listbox_listItems.value.map((item) => (item.selected = selectedValue));
    // this.setSelectedItems();
  }

  changeKeyword() {
    this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe((value: string) => {
      if (value.length < 3) return;
      this.isLoading.next(true);
      this.getListBoxData(value);
    });
  }
}
