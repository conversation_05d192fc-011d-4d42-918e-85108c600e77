// src/app/core/utils/__tests__/test-helpers.ts
// Shared test utilities for core/utils tests

export class TestHelpers {
  /**
   * Create a mocked service with all methods mocked
   */
  static createMockService<T>(methods: Partial<T> = {}): jest.Mocked<T> {
    return methods as jest.Mocked<T>;
  }

  /**
   * Create a DOM element for testing
   */
  static createTestElement(tag: string = 'div'): HTMLElement {
    return document.createElement(tag);
  }

  /**
   * Create a mock event for testing
   */
  static createMockEvent(type: string, target?: any): Event {
    const event = new Event(type);
    if (target) {
      Object.defineProperty(event, 'target', { value: target });
    }
    return event;
  }

  /**
   * Create a mock file for testing
   */
  static createMockFile(content: string, name: string = 'test.csv', type: string = 'text/csv'): File {
    const blob = new Blob([content], { type });
    return new File([blob], name, { type });
  }

  /**
   * Mock clipboard API
   */
  static mockClipboard(writeTextMock?: jest.Mock): void {
    const mock = writeTextMock || jest.fn().mockResolvedValue(undefined);
    Object.assign(navigator, {
      clipboard: { writeText: mock },
    });
  }

  /**
   * Mock URL.createObjectURL
   */
  static mockCreateObjectURL(returnValue: string = 'blob:fake-url'): jest.Mock {
    const mock = jest.fn(() => returnValue);
    global.URL.createObjectURL = mock;
    return mock;
  }

  /**
   * Mock document methods
   */
  static mockDocumentMethods(): {
    createElement: jest.Mock;
    appendChild: jest.Mock;
    removeChild: jest.Mock;
  } {
    const createElementMock = jest.fn();
    const appendChildMock = jest.fn();
    const removeChildMock = jest.fn();

    jest.spyOn(document, 'createElement').mockImplementation(createElementMock);
    jest.spyOn(document.body, 'appendChild').mockImplementation(appendChildMock);
    jest.spyOn(document.body, 'removeChild').mockImplementation(removeChildMock);

    return {
      createElement: createElementMock,
      appendChild: appendChildMock,
      removeChild: removeChildMock,
    };
  }

  /**
   * Create a mock anchor element
   */
  static createMockAnchor(): any {
    return {
      setAttribute: jest.fn(),
      style: { visibility: '' },
      click: jest.fn(),
    };
  }

  /**
   * Reset all mocks
   */
  static resetMocks(): void {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  }

  /**
   * Wait for async operations
   */
  static async wait(ms: number = 0): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a test service with TestBed
   */
  static createTestService<T>(serviceClass: new (...args: any[]) => T): T {
    return new serviceClass();
  }

  /**
   * Create a test service with dependency injection
   */
  static createTestServiceWithDI<T>(
    serviceClass: new (...args: any[]) => T,
    dependencies: any[] = [],
  ): T {
    return new serviceClass(...dependencies);
  }

  /**
   * Mock console methods
   */
  static mockConsole(): {
    log: jest.Mock;
    error: jest.Mock;
    warn: jest.Mock;
  } {
    const logMock = jest.fn();
    const errorMock = jest.fn();
    const warnMock = jest.fn();

    jest.spyOn(console, 'log').mockImplementation(logMock);
    jest.spyOn(console, 'error').mockImplementation(errorMock);
    jest.spyOn(console, 'warn').mockImplementation(warnMock);

    return {
      log: logMock,
      error: errorMock,
      warn: warnMock,
    };
  }

  /**
   * Create a mock promise that resolves after delay
   */
  static createDelayedPromise<T>(value: T, delay: number = 100): Promise<T> {
    return new Promise(resolve => setTimeout(() => resolve(value), delay));
  }

  /**
   * Create a mock promise that rejects after delay
   */
  static createDelayedReject<T>(error: any, delay: number = 100): Promise<T> {
    return new Promise((_, reject) => setTimeout(() => reject(error), delay));
  }
}

/**
 * Custom Jest matchers for common assertions
 */
export const customMatchers = {
  toBeValidDate: (received: any) => {
    const pass = received instanceof Date && !isNaN(received.getTime());
    return {
      pass,
      message: () => `Expected ${received} to be a valid Date`,
    };
  },

  toBeValidNumber: (received: any) => {
    const pass = typeof received === 'number' && !isNaN(received);
    return {
      pass,
      message: () => `Expected ${received} to be a valid number`,
    };
  },

  toHaveBeenCalledWithString: (received: jest.Mock, expected: string) => {
    const calls = received.mock.calls;
    const pass = calls.some(call => call[0] === expected);
    return {
      pass,
      message: () => `Expected mock to have been called with "${expected}"`,
    };
  },
};

// Extend Jest expect
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidDate(): R;

      toBeValidNumber(): R;

      toHaveBeenCalledWithString(expected: string): R;
    }
  }
}
