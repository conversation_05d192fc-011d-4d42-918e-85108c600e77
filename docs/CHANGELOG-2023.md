# Backoffice Changelog - 2023

## [1.6.2](https://gitlab.com/maxxi-agro/atom/compare/v1.6.1...v1.6.2) (2023-12-27)


### Features

* get filter status purchase order ([410eb25](https://gitlab.com/maxxi-agro/atom/commit/410eb25457d378e1092b68f3e162daf6b6c34b84))
* **MTMAI-2498, MTMAI-2499, MTMAI-2505:** filter status in history po ([4e5674b](https://gitlab.com/maxxi-agro/atom/commit/4e5674b2881b627ac86db042091c3ab79f143c3c))
* **MTMAI-2501, MTMAI-2502, MTMAI-2503, MTMAI-2504:** reason in detail purchase order ([b0dc58b](https://gitlab.com/maxxi-agro/atom/commit/b0dc58bc97a6235df89fd07f6c0a42ed95767827))


### Bug Fixes

* **MTMAI-2807:** show reason cancel status ([ce2748b](https://gitlab.com/maxxi-agro/atom/commit/ce2748bc109b503fd0a9a1927a02932212301585))
* **MTMAI-2807:** tabing for detail purchase order ([05443e7](https://gitlab.com/maxxi-agro/atom/commit/05443e7d4676b524b9bac15310f32d9bf14006ad))
* **MTMAI-2861, MTMAI-2858:** view detail po ([1f35679](https://gitlab.com/maxxi-agro/atom/commit/1f35679508a74d38358acd6bff3d7120950b046a))
* **MTMAI-2861, MTMAI-2858:** view detail po ([35ae3a9](https://gitlab.com/maxxi-agro/atom/commit/35ae3a9687f4072c6f6b756d01b79870cbabdf37))
* **MTMAI-2926:** hide show tab in detail po ([bb8b9f9](https://gitlab.com/maxxi-agro/atom/commit/bb8b9f9f5053f4ca1a9e0a13ea3e4af815e9971d))
* **purchase order:** change endpoint list and counter purchase order ([c9098c5](https://gitlab.com/maxxi-agro/atom/commit/c9098c52e6a701e41d2fd3c331fc149041a3c9a2))
* **purchase order:** fix approval by in detail purchase order ([e0360ac](https://gitlab.com/maxxi-agro/atom/commit/e0360aca98f65ab1da562013322d6eaeb9499d30))
* **purchase order:** hide show tab in purchase order ([ede2e7d](https://gitlab.com/maxxi-agro/atom/commit/ede2e7d40c998566d189abac65ca4326b8f6d535))
* **purchase order:** price summary discount sales ([ba62ca1](https://gitlab.com/maxxi-agro/atom/commit/ba62ca1ea7ee75a3bd5c7afa3cbe9212ce4e5b1e))

## [1.6.1](https://gitlab.com/maxxi-agro/atom/compare/v1.6.0...v1.6.1) (2023-11-14)


### Features

* **MTMAI-2393, MTMAI-2399, MTMAI-2400, MTMAI-2409:** crud master data ([fe2cff7](https://gitlab.com/maxxi-agro/atom/commit/fe2cff74d93dc53fd88123ad5f5691be55e13bc3))
* **MTMAI-2397, MTMAI-2398:** add menu list master data ([88bc8fa](https://gitlab.com/maxxi-agro/atom/commit/88bc8fa53c9aa57fb8e9888d590ca3db90d899bd))
* **MTMAI-2468:** validate name master data ([306b17b](https://gitlab.com/maxxi-agro/atom/commit/306b17b77581710ff3b69f84d16982dfc8f485de))


### Bug Fixes

* **master-data:** counting master data list ([f0a42f3](https://gitlab.com/maxxi-agro/atom/commit/f0a42f3bad3592bf042cf08dbc0781c65ac3aa0f))
* **master-data:** modal confirm ([5d0f53a](https://gitlab.com/maxxi-agro/atom/commit/5d0f53a5ace3de375b30c6688d1cea94dbbccd92))
* **MTMAI-1817:** distributor log list data use link distributor_id ([f43958a](https://gitlab.com/maxxi-agro/atom/commit/f43958a83b01c463bc7abfe698117680f3c27af9))
* **MTMAI-1822:** Card log show link got edit request finance ([5c68a3d](https://gitlab.com/maxxi-agro/atom/commit/5c68a3d053ab2e1f23eed086f0062d615d875351))
* **MTMAI-1966:** wording filter purchase order ([0826943](https://gitlab.com/maxxi-agro/atom/commit/0826943feca557486fe6ece2f0ab95539ed48bb8))
* **MTMAI-2383:** brand list item duplicated on create bulk ([a35dea4](https://gitlab.com/maxxi-agro/atom/commit/a35dea4b8892571424a9a37d8560085a4220ef5c))
* **MTMAI-2436:** Image preview portrait image ([c77df0d](https://gitlab.com/maxxi-agro/atom/commit/c77df0d2a4c9e97a36964959015605f937f45c26))
* **MTMAI-2473:** role settings missing icon surat perintah muat ([9819303](https://gitlab.com/maxxi-agro/atom/commit/981930311a83a58798b6d79f228e19e757125e83))
* **MTMAI-2475:** sorting created date ([e1b09e4](https://gitlab.com/maxxi-agro/atom/commit/e1b09e49fc53aa5e4acc1a38c6082a75e24d96e9))
* **MTMAI-2476:** disable enable CTA filter list all page ([60d1e40](https://gitlab.com/maxxi-agro/atom/commit/60d1e40e4d0f7e8215171c7a120af9e9c4f52347))
* **MTMAI-2477, MTMAI-2478:** fix validate form master data ([db22c93](https://gitlab.com/maxxi-agro/atom/commit/db22c930f9f58768c24c0f9382df5ea35e06c2a2))
* **MTMAI-2479:** wording cancel form master data ([86920b4](https://gitlab.com/maxxi-agro/atom/commit/86920b4e6505a7ac178309b52a2ef0981abc0411))
* **MTMAI-2482:** fix filter combination ([395e811](https://gitlab.com/maxxi-agro/atom/commit/395e81136801f751b271e990f5d3c9456d6d1cfc))
* **MTMAI-2483:** checking noteview detail so ([6094c90](https://gitlab.com/maxxi-agro/atom/commit/6094c9094e93809fda99c2b77108277ce76671bf))
* **MTMAI-2486:** show tab spm in detail sales order ([80f76f8](https://gitlab.com/maxxi-agro/atom/commit/80f76f8fe31dd21deabcfbb2e3db20c61b1a66ec))
* **MTMAI-2487, MTMAI-2485:** fix form master data ([4c33ee5](https://gitlab.com/maxxi-agro/atom/commit/4c33ee5874d013448164fcb816b70a4ff51cbe7a))
* **MTMAI-2490:** validate form master data ([09b1225](https://gitlab.com/maxxi-agro/atom/commit/09b12255dea21280df6ad533e12d380eaa22ab23))
* **MTMAI-2492:** Create SPM form input counter order add state loading fetch ([91ccf6b](https://gitlab.com/maxxi-agro/atom/commit/91ccf6b3d428076c2065618ff7e5dd7e9ade8778))
* **MTMAI-2511:** validate name update mode ([2ced285](https://gitlab.com/maxxi-agro/atom/commit/2ced2856fc29be75149a7abc26698c6bd96ac04f))
* **MTMAI-2515:** add noteview before expired detail so outstanding ([0112cde](https://gitlab.com/maxxi-agro/atom/commit/0112cde356fffefd666d622fcdcd58f772731c67))
* **MTMAI-2515:** error console productUnscan ([44ee32b](https://gitlab.com/maxxi-agro/atom/commit/44ee32b4a415c834cc35c793a4a999aefaa5423d))
* **REGISTER MAC - FORM:** Handle get cakupan wilayah list by input search ([15343ad](https://gitlab.com/maxxi-agro/atom/commit/15343ad9d2dbf03067a1b708744b0ac57db80ded))
* **SPM - UNSCAN:** handle scan error response ([ed4f2f8](https://gitlab.com/maxxi-agro/atom/commit/ed4f2f8f3cb3b7eedeeb71b4b6b778fed406a40f))
* **uncommit:** uncomitted file ([a62a968](https://gitlab.com/maxxi-agro/atom/commit/a62a96873c7bcb6e6409c6f13411fd5bd8d3dfb4))
* **validation:** validation name in master data form ([5628784](https://gitlab.com/maxxi-agro/atom/commit/56287845470ee7fbe959d4a9c43668272fd28152))

## [1.6.0](https://gitlab.com/maxxi-agro/atom/compare/v1.5.1...v1.6.0) (2023-10-23)


### ⚠ BREAKING CHANGES

* **PE FORM:** update PE payload add group_setup_id
* **PE FORM:** create/update PE payload document changes; add seq, url

### Features

* **MTMAI-2143:** PE form enhancement map assigned product data to noteview ([e737b58](https://gitlab.com/maxxi-agro/atom/commit/e737b58cf6695a0bc04e12866aa0290bf112e890))
* **MTMAI-2145:** product exclusive form sub-area dialog component ([22ae935](https://gitlab.com/maxxi-agro/atom/commit/22ae935ce1ea99e6336e332679b9e1ddcc03e4f3))
* **MTMAI-2148:** Image upload component error state enhancement ([1699b2a](https://gitlab.com/maxxi-agro/atom/commit/1699b2a292f316fb8ce6153db15c9363fc766302))
* **PE FORM:** create/update PE payload document changes; add seq, url ([6e49c24](https://gitlab.com/maxxi-agro/atom/commit/6e49c24eeeac398a06770fa1685d6dfdf984b80f))
* **PE FORM:** update PE payload add group_setup_id ([14bac9a](https://gitlab.com/maxxi-agro/atom/commit/14bac9a990d5d512fdb767dad247f593d9a2af75))


### Bug Fixes

* detail spm ([104abc6](https://gitlab.com/maxxi-agro/atom/commit/104abc664666f8e917a13ae579fc939990e311a5))
* **MAC REGISTER:** Remove max-length input NPWP ([41b8d11](https://gitlab.com/maxxi-agro/atom/commit/41b8d11a51658af2871335051644f71249d2cc7f))
* **MTMAI-2123:** PE list table handle action view detail modal ([02b62a5](https://gitlab.com/maxxi-agro/atom/commit/02b62a5d4430de96335b9884c4840ac432d6c03c))
* **MTMAI-2143:** PE form get selected variant value in input listbox ([8d07a74](https://gitlab.com/maxxi-agro/atom/commit/8d07a74738c6697bcff0d0a45af86b7e582efb1a))
* **MTMAI-2143:** PE form input varian handle indeterminate checked item ([ce041da](https://gitlab.com/maxxi-agro/atom/commit/ce041dab4a0a8cf033faf15c2469f4ed90a72f67))
* **MTMAI-2194:** Product exclusive create/edit change payload ([2f79037](https://gitlab.com/maxxi-agro/atom/commit/2f7903724343b83b4eb0ae19ac216587d26942f9))
* **MTMAI-2227:** note view product not process with admin center ([e5f85df](https://gitlab.com/maxxi-agro/atom/commit/e5f85dfbc490537f4c7cbacea4073fd739ff76ea))
* **MTMAI-2338:** PE form edit - remove initial value document ([9167027](https://gitlab.com/maxxi-agro/atom/commit/91670275de1fc2fad16d57758a16b4601067278e))
* **MTMAI-2339:** PE form cancel confirmation dialog wording ([808858a](https://gitlab.com/maxxi-agro/atom/commit/808858af5c4e3ef040bd4e229735577e0c42be50))
* **MTMAI-2340:** show sub area in history product exclusive ([7afaae8](https://gitlab.com/maxxi-agro/atom/commit/7afaae836ae3dc20be26c025941be35c9c343bd0))
* **MTMAI-2341:** PE form limit document upload to 3 ([f8276a1](https://gitlab.com/maxxi-agro/atom/commit/f8276a111621d9b15c73d7889591d36eb4310c81))
* **MTMAI-2347:** show sub area in history distributor exclusive & refactor on search ([8589870](https://gitlab.com/maxxi-agro/atom/commit/85898702042f3fb2054f82a972a271612b0b71ba))
* **MTMAI-2348:** PE Form handle removed product brand ([fbf321c](https://gitlab.com/maxxi-agro/atom/commit/fbf321c6e1ccbf1c46dd847f91c5cec939367b85))
* **MTMAI-2350:** PE Form handle confirm cancel dialog ([ffa1bf7](https://gitlab.com/maxxi-agro/atom/commit/ffa1bf7171eb6a77e5e11b259bed07b8aac47194))
* **MTMAI-2353:** handle assigned product on bulking product ([0a2861b](https://gitlab.com/maxxi-agro/atom/commit/0a2861b9c5915919c820dfe25e110d4ef083e0b2))
* **MTMAI-2353:** handle bulk assigned product noteview ([ef6fa9d](https://gitlab.com/maxxi-agro/atom/commit/ef6fa9d2d47c2dc4d68d4ec0665a62c9ef9f6e90))
* **MTMAI-2354:** PE form set area id on bulk product field item ([6dd555c](https://gitlab.com/maxxi-agro/atom/commit/6dd555ccb5955898a8d42197e9dcca7e6764f157))
* **MTMAI-2358:** confirm add product dialog render coverage name type ([456921e](https://gitlab.com/maxxi-agro/atom/commit/456921e55d66077f76e957146dc809f67fd21132))
* **MTMAI-2360, MTMAI-2359:** fix detail product exclusive & distributor exclusive ([4b84900](https://gitlab.com/maxxi-agro/atom/commit/4b84900ccb431773a3539f867342eb33871be2eb))
* **MTMAI-2362:** PE Form get assigned product payload add distributor_id ([9f5b269](https://gitlab.com/maxxi-agro/atom/commit/9f5b2695c686e3196084f2a1b9361e3b209474eb))
* **MTMAI-2364:** remove duplicated assigned product on change coverage ([519b3ed](https://gitlab.com/maxxi-agro/atom/commit/519b3edafaed69c3e3e1ec80ccea4359ca052e50))
* pagination url params ([7bf5e15](https://gitlab.com/maxxi-agro/atom/commit/7bf5e15fd7e7dd9667a09793baeedf730a857c37))
* refactor onsearch ([496dc69](https://gitlab.com/maxxi-agro/atom/commit/496dc69eb65a46dafbbd16905ad8e2a5473c6716))
* refactor retailer ([b2e6799](https://gitlab.com/maxxi-agro/atom/commit/b2e67996c7fc9e2b474a27389b41c7b563651766))
* refactor search spm ([cfc9736](https://gitlab.com/maxxi-agro/atom/commit/cfc9736427791a3bf0bc1107a3f45add947c90f2))
* **Register MAC:** Input list cakupan search text filter ([10aa826](https://gitlab.com/maxxi-agro/atom/commit/10aa8267a2fa6ee4d9039549697478fa823f2335))
* **Register MAC:** Input list cakupan search text filter ([94a8098](https://gitlab.com/maxxi-agro/atom/commit/94a809880540e1fe12cb36d7642bc5b0269c5f3e))
* sub area product exclusive list & detail MTMAI-2337, MTMAI-2336 ([60da2fa](https://gitlab.com/maxxi-agro/atom/commit/60da2fa8ddcf6002ff88983aed299f125363e2c4))

## [1.5.1](https://gitlab.com/maxxi-agro/atom/compare/v1.4.2...v1.5.1) (2023-10-10)


### ⚠ BREAKING CHANGES

* **Filter-Table:** Add input reset label on filter-table component MTMAI-1891

### Features

* add privelege menu spm ([0fcbbf9](https://gitlab.com/maxxi-agro/atom/commit/0fcbbf9d258ce0b6b0e5d4f3ddd35b34210bbc49))
* add privilege for tab product unscan so MTMAI-2169 ([54dea94](https://gitlab.com/maxxi-agro/atom/commit/54dea9416dec9ba45788947c144617dc5935d72f))
* add product unscan list MTMAI-1265 ([bc50586](https://gitlab.com/maxxi-agro/atom/commit/bc50586221354333f9c8869fcb9d528f2607acf3))
* add snackbar qr batch form create MTMAI-1215 ([2885a13](https://gitlab.com/maxxi-agro/atom/commit/2885a135f111ee91cf4ea612e1d3d0820c2d94e1))
* **approve-partial:** handle approval with reason form value MTMAI-1581 ([f54d36d](https://gitlab.com/maxxi-agro/atom/commit/f54d36df7950d584b3cc8c32e47ac2f085bedef9))
* checking if approve spm with complete scan product ([a45ec59](https://gitlab.com/maxxi-agro/atom/commit/a45ec5986eaaa17a3c48284f569d640d26df9e61))
* component card header information ([01990f4](https://gitlab.com/maxxi-agro/atom/commit/01990f41b3e089d029855844b4987fe6babac043))
* component note view with detail ([46e5e90](https://gitlab.com/maxxi-agro/atom/commit/46e5e90c1e75007540a6fa93507b23a61f6f71a3))
* counting list SPM, SO, PO MTMAI-1903 ([098a42f](https://gitlab.com/maxxi-agro/atom/commit/098a42fefabcc600811b5d5d9d863625447b8bc7))
* create spm page component; MTMAI-1186 ([851fb43](https://gitlab.com/maxxi-agro/atom/commit/851fb43a31ae9c441c042dfe7eb13d98bb1e099f))
* detail process scan MTMAI-1190, MTMAI-1308, MTMAI-1309 ([f924b29](https://gitlab.com/maxxi-agro/atom/commit/f924b29c10341bddc316fc14156b0982fefc2e51))
* Detail QR Batch page; add modal confirm/response dialog. Integrate endpoint download;  MTMAI-1221 MTMAI-1315 ([77b524f](https://gitlab.com/maxxi-agro/atom/commit/77b524f4b509ae533e90983a0775e0dd196cfd67))
* Detail QR batch page; batch info card MTMAI-1221 ([10fcc1c](https://gitlab.com/maxxi-agro/atom/commit/10fcc1cc6febcb77b2b19560de79fb488b72f5eb))
* detail reset product scan SPM MTMAI-1191, MTMAI-1301 ([d95581e](https://gitlab.com/maxxi-agro/atom/commit/d95581e54dcea19ed0d312dc90f6afcec7a99154))
* detail so approval hq ([a7ef666](https://gitlab.com/maxxi-agro/atom/commit/a7ef666b07fd1c9246a0d546738ba44b2d7ec418))
* detail with image component ([89ccf3d](https://gitlab.com/maxxi-agro/atom/commit/89ccf3d1ee8401d5cacd8b3914c1651e622c4418))
* **detail-retailer:** enhancement informasi kemitraan card; add NPWP value for R1 ([ef22859](https://gitlab.com/maxxi-agro/atom/commit/ef22859039ebb5d604c411aa52691a9749f4e93a))
* enhance detail SO MTMAI-1265, MTMAI-1289, MTMAI-1290, MTMAI-1291, MTMAI-1292, MTMAI-1293, MTMAI-1294 ([25e7cdb](https://gitlab.com/maxxi-agro/atom/commit/25e7cdb1fd25b737568aa8dc8fad18a05a3bdcdc))
* enhance detail spm MTMAI-1595, MTMAI-1596, MTMAI-1597 ([d789262](https://gitlab.com/maxxi-agro/atom/commit/d789262c414e5c460b1456416d0751b0595cf6c5))
* enhance distributor exclusive MTMAI-2155, MTMAI-2196 ([cba3b91](https://gitlab.com/maxxi-agro/atom/commit/cba3b912c8d6c24a159a6dfb6838b8f631bb9bbc))
* enhance hostory distributor exclusive MTMAI-2157, MTMAI-2197 ([865fa35](https://gitlab.com/maxxi-agro/atom/commit/865fa357d49291f7d36f253abe3e1d391305b692))
* enhance product exclusive and history MTMAI-2153, MTMAI-2195 ([d824191](https://gitlab.com/maxxi-agro/atom/commit/d824191a8c842d9e8d94c109accc373e32b6ca91))
* enhance product exclusive distributor MTMAI-2123, MTMAI-2191, MTMAI-2193, MTMAI-2125, MTMAI-2127, MTMAI-2129, MTMAI-2151 ([5e8d01b](https://gitlab.com/maxxi-agro/atom/commit/5e8d01b5c06ef15b77eaa41828507810a312f985))
* enhance SO detail and spm MTMAI-1557, MTMAI-1558, MTMAI-1559, MTMAI-1560, MTMAI-1584, MTMAI-1587, MTMAI-1611 ([812a811](https://gitlab.com/maxxi-agro/atom/commit/812a81129852bb84fab3573ce3068c86e43d60ef))
* enhance so sedang menunggu MTMAI-1578, MTMAI-1580, MTMAI-1609, MTMAI-1610 ([277d85e](https://gitlab.com/maxxi-agro/atom/commit/277d85e504b6eebb22b8b2a8e68ae21398417848))
* **Filter-Table:** Add input reset label on filter-table component MTMAI-1891 ([45e31e8](https://gitlab.com/maxxi-agro/atom/commit/45e31e8645de3b11a25497e4317006acc3e93d57))
* Form add qr batch wip; add component and page ([a93c522](https://gitlab.com/maxxi-agro/atom/commit/a93c522d62da3f23b20dd1b5c730093fab5cf63c))
* handle create qr batch form; post and get response wip MTMAI-1210 MTMAI-1211 MTMAI-1213 MTMAI-1214 MTMAI-1314 ([40ec5cb](https://gitlab.com/maxxi-agro/atom/commit/40ec5cbde4214a6b7fd4fc3d8d4bc6c9afa599b1))
* handle link cancel/create spm page; MTMAI-1186 ([d72b1ea](https://gitlab.com/maxxi-agro/atom/commit/d72b1eac2e91de2a520868197db98fa7b6b15943))
* **handle unmet product:** Approval so-tag form modal reason MTMAI-1588 ([195e0a8](https://gitlab.com/maxxi-agro/atom/commit/195e0a8b484637b304cf4ba1e0b411657eed9392))
* **info-card:** Retailer detail MAC user informasi kemitraan MTMAI-1569 ([7e3919a](https://gitlab.com/maxxi-agro/atom/commit/7e3919a30f696bed5c102b41aaa11e39487f20ee))
* integrate noteview spm if status selesai sebagian SPM MTMAI-1591 ([5da8616](https://gitlab.com/maxxi-agro/atom/commit/5da861629aa8666a0a9482bd522c9f31e70e37be))
* **integrate-api:** add confirm modal; reason modal; handle post approval MTMAI-1599 MTMAI-1600 MTMAI-1601 ([4bcf8ae](https://gitlab.com/maxxi-agro/atom/commit/4bcf8ae23dc91179518c6cc9015f56bc40b40c1c))
* **integrate-api:** approval so-tag map product order data; MTMAI-1583 ([a196899](https://gitlab.com/maxxi-agro/atom/commit/a1968998eeee328f65913a26fe44589e71b50365))
* **integrate-API:** approvall SO-TAG handle reject/approve MTMAI-1583 ([d678f30](https://gitlab.com/maxxi-agro/atom/commit/d678f30e2a9434da5538c6002d4d2bc4d69f7be3))
* integration menunggu konfirmasi spm and fix detail spm breadcrumb MTMAI-1594 ([ef1126d](https://gitlab.com/maxxi-agro/atom/commit/ef1126db075bf7b337a0bdd1a6e79f2b9f8f13d3))
* integration price summary MTMAI-1527 ([7b18a05](https://gitlab.com/maxxi-agro/atom/commit/7b18a05c132c5a4204d514258162f72283d9ff0e))
* intigration privilege detail spm ([c64b330](https://gitlab.com/maxxi-agro/atom/commit/c64b330c282d9aa0d5a2680e84a8ce26a8b9a4f5))
* **listbox-filter:** Add filter by key property ([d44e6af](https://gitlab.com/maxxi-agro/atom/commit/d44e6af49dd9dfe2c7602487146fe1c606112906))
* **MAC-register:** Modal registration response error/success handler MTMAI-1572 ([7db932e](https://gitlab.com/maxxi-agro/atom/commit/7db932e9a5835e38c0a7008a8f2839d47f8962e3))
* **MAC-Registration:** Add confirm/response dialog; handle success MTMAI-1572 MTMAI-1608 ([f35bedc](https://gitlab.com/maxxi-agro/atom/commit/f35bedc56f42b26fac0eb49887e89d66003d8687))
* **MTMAI-1566:** List retailer enhance filter for MAC mitra MTMAI-1568 ([48abcbf](https://gitlab.com/maxxi-agro/atom/commit/48abcbf22336678121b0deab9315fdeccf011742))
* **MTMAI-1572:** retailer MAC registration ([f323401](https://gitlab.com/maxxi-agro/atom/commit/f3234017d8012bcd0db648bad219860e12c91f03))
* post create batch qr; MTMAI-1210 ([784fa5f](https://gitlab.com/maxxi-agro/atom/commit/784fa5f89b8c19aec44a8399040dd8c7d90eaf93))
* **price summary:** Render price summary card MTMAI-1583 ([6f09cc9](https://gitlab.com/maxxi-agro/atom/commit/6f09cc953788fc24f90f1cba49e8ae68c24149b0))
* privilege btn save spm for admin ([484f02f](https://gitlab.com/maxxi-agro/atom/commit/484f02f454bf2579ad00597e426a62aac7d1acb0))
* privilege tab SO pending ([7237900](https://gitlab.com/maxxi-agro/atom/commit/7237900efdf7dd0a313d3f21c4fe5baad60987f0))
* qr batch create form; cancel remove item handler MTMAI-1215 ([268cfb5](https://gitlab.com/maxxi-agro/atom/commit/268cfb5e3cd6093ebc719e4831dfad49d1ff13a6))
* QR batch detail get list produk wip ([c0cf63c](https://gitlab.com/maxxi-agro/atom/commit/c0cf63c663f8a67284b6722cf46255104f75ee82))
* qr batch form create; handle value current-selected brand on add/remove MTMAI-1215 ([b5d2c49](https://gitlab.com/maxxi-agro/atom/commit/b5d2c493557a22c72536afe6cc14aee523adf453))
* QR Product list close modal response on error data; MTMAI-1208 ([e01e9c7](https://gitlab.com/maxxi-agro/atom/commit/e01e9c73fa5132c111f128c607cb37ed5f77ddcd))
* QR Product list close modal response on error data; MTMAI-1208 ([2b97f44](https://gitlab.com/maxxi-agro/atom/commit/2b97f440bd7de38afbc866bb6ae8e4d96ee7de0c))
* QR Product list, page filter & search MTMAI-1208 ([c13c2eb](https://gitlab.com/maxxi-agro/atom/commit/c13c2ebff0eccf88766e3a64f35385f7f03968e6))
* QR Product list, page filter & search MTMAI-1208 ([4fd68c2](https://gitlab.com/maxxi-agro/atom/commit/4fd68c29fa0233e6e53dc68db0b0e525e6f6ef9c))
* QR produk list add batch download modal confirm/response MTMAI-1208 ([2f996fe](https://gitlab.com/maxxi-agro/atom/commit/2f996fe4ed17c914f2e69a4b8df627c73f9e7ee0))
* QR produk list add batch download modal confirm/response MTMAI-1208 ([255156c](https://gitlab.com/maxxi-agro/atom/commit/255156c2ff2708a94ca12241a49daa124b09d5e1))
* **qr-batch form:** Add limit qty(5500) input per-batch; Add limit(5) select brand per-batch ([7a2dafa](https://gitlab.com/maxxi-agro/atom/commit/7a2dafa8e39078f5801c605c94fd4e28ceabaca1))
* **re-map data detail so spm:** handle post form create spm MTMAI-1300 ([67456a4](https://gitlab.com/maxxi-agro/atom/commit/67456a412fdfe3343b8b183783fbe58d84379c84))
* **register-mac form:** Add Input cakupan wilayah MTMAI-1572 ([23847b5](https://gitlab.com/maxxi-agro/atom/commit/23847b5b27966a79b147b893f6fa142a434538f5))
* **register-mac form:** render form data MTMAI-1572 ([2a00c77](https://gitlab.com/maxxi-agro/atom/commit/2a00c77055b8d17dd69ab18ba46fd11f2769eab3))
* **reject so-tag:** add  reject so-tag modal MTMAI-1581 ([c51d9ea](https://gitlab.com/maxxi-agro/atom/commit/c51d9eab1912d2480d5bb806de99aee604a1a8f0))
* **reject SPM TAG:** add reject dialog; form reason dialog; handle post reject spm-tag MTMAI-1592 MTMAI-1599 ([39687fd](https://gitlab.com/maxxi-agro/atom/commit/39687fddbdb4f225523939074b916efda700fb28))
* **reject-spm:** modal reject form reject handler MTMAI-1599 ([f3fa6f5](https://gitlab.com/maxxi-agro/atom/commit/f3fa6f5db17449b6b10b04e85a1f2b925ec7c1de))
* **retailer detail:** add badge MAC; add menu register MAC MTMAI-1569 ([87a71da](https://gitlab.com/maxxi-agro/atom/commit/87a71da0d3302afdec052534d697aa824ed7d0b1))
* setup qr batch form, add input counter, add modal brand list, render table list MTMAI-1210 ([3d8fad8](https://gitlab.com/maxxi-agro/atom/commit/3d8fad8e94460f0d27c1f2e129ca46bc409cfd95))
* spm form create render get data; wip MTMAI-1186 ([32ef1fe](https://gitlab.com/maxxi-agro/atom/commit/32ef1fe690fa920b065cec7f1b1aebb5fc4bce54))
* SPM list & detail MTMAI-1183 ([6686623](https://gitlab.com/maxxi-agro/atom/commit/6686623eab50da00de30e469a13bbf2f5ef6697a))
* spm list in detail SO ([5ff10b3](https://gitlab.com/maxxi-agro/atom/commit/5ff10b3d2bc8a32898b31724e83da81373a933b4))
* spm menunggu konfirmasi list MTMAI-1590, MTMAI-1593 ([320b504](https://gitlab.com/maxxi-agro/atom/commit/320b504dcf942c28709a67b9e805b782703b9ce3))
* tab child component and fix load detail api ([80dfccb](https://gitlab.com/maxxi-agro/atom/commit/80dfccbf209646b43ef0a527336a915159efea5d))
* tab DO in detail spm and redirect after save spm ([b2a9255](https://gitlab.com/maxxi-agro/atom/commit/b2a9255eda854b284a63a5f8ad5790a34fa7bc3c))
* **tab-component:** make tab more usable ([adf94ef](https://gitlab.com/maxxi-agro/atom/commit/adf94ef6f7c0359e1b12896249767d020d3401b5))
* **tab-component:** Tab container use componentOutlet to load child component ([9f003d0](https://gitlab.com/maxxi-agro/atom/commit/9f003d09e529f45d71c580c78392b6c40830979b))
* **table data list:** Add table list; column kemitraan MTMAI-1567 ([d868515](https://gitlab.com/maxxi-agro/atom/commit/d868515fb50ddcba0016f2ab297a8523c8d541c7))
* **ui-slicing:** add approval sales-order TAG MTMAI-1582 ([b7ddaec](https://gitlab.com/maxxi-agro/atom/commit/b7ddaecc40100a1aa3d13bc2eda2a319d49ca596))
* **UI:** Add component page MAC Registration MTMAI-1572 ([bc2ea3a](https://gitlab.com/maxxi-agro/atom/commit/bc2ea3ac8a82dd6ac71869dff3888e41300ddc4a))
* **ui:** Approval TAG add section information component; init get form data MTMAI-1598 ([3e49418](https://gitlab.com/maxxi-agro/atom/commit/3e49418aa8c1553586b4ce81f42e715460a2833e))


### Bug Fixes

*  mapping updated value of target penjualan - penagihan kredit; modal detail piutang MTMAI-1510 MTMAI-1513 ([70ef682](https://gitlab.com/maxxi-agro/atom/commit/70ef6825880663e595d01904471ced6d179babba))
*  render edited value data, cabang-usaha|informasi-supplier MTMAI-1433 MTMAI-1434 ([058bd94](https://gitlab.com/maxxi-agro/atom/commit/058bd94e9109d278bf975a693427914f16fcdbf5))
*  render edited value data, cabang-usaha|informasi-supplier MTMAI-1433 MTMAI-1434 ([2aeb0a8](https://gitlab.com/maxxi-agro/atom/commit/2aeb0a8ca0c6bcb9f282a3eb9613468614430388))
* add empty state if no dokumen pembaruan data; edit finance request MTMAI-1516 ([18fa69b](https://gitlab.com/maxxi-agro/atom/commit/18fa69b10294492cc89ade34c720438590bd628b))
* add summary counting SPM and SO MTMAI-2038, MTMAI-2037 ([cf0840d](https://gitlab.com/maxxi-agro/atom/commit/cf0840d6640cd4b6ab2e73c1a0c24b3f0ac73231))
* alamat pengiriman loading card MTMAI-1538 ([040e5d3](https://gitlab.com/maxxi-agro/atom/commit/040e5d33e46cd337b78ab354742c972eeb062fa1))
* alamat pengiriman mapping data fix MTMAI-1516 ([c292684](https://gitlab.com/maxxi-agro/atom/commit/c292684eb2b206bdd22141efe32f6de9fe73f375))
* **Approval-SO:** handle calculate subtotal-price product with discount ([d40bd60](https://gitlab.com/maxxi-agro/atom/commit/d40bd6001f2bdd14e7c0558e40009af811a860b7))
* **approval-tag:** post approval so/spm payload changes ([4bbdcc7](https://gitlab.com/maxxi-agro/atom/commit/4bbdcc7a1589ffa60f2e64e2ab6c88fb0ed9f878))
* assign warehouse in detail po need full fill ([be67de0](https://gitlab.com/maxxi-agro/atom/commit/be67de0708f1358ad1cfc9e1bce73c1400ba3769))
* breadcrumb detail spm ([49a81c1](https://gitlab.com/maxxi-agro/atom/commit/49a81c1871905c5170d9195df8e333484258e393))
* btn approve center spm ([2eb2df1](https://gitlab.com/maxxi-agro/atom/commit/2eb2df17b7e872f247d2c168702e9cfa5de31686))
* btn save spm in detail spm partial completed MTMAI-2223 ([7398ecd](https://gitlab.com/maxxi-agro/atom/commit/7398ecdf3bbcf6568d49d8f7d586c7dbeccb9cc5))
* btn save spm modal ([2a06ffd](https://gitlab.com/maxxi-agro/atom/commit/2a06ffde376334ce76f9a3a891ac6b40f5294169))
* **Card header:** Remove unwanted wording ([ce24258](https://gitlab.com/maxxi-agro/atom/commit/ce2425865d8443ae9050b1f4220c59c32bf22197))
* change api approve finance ([59d2c6a](https://gitlab.com/maxxi-agro/atom/commit/59d2c6a3fbe6fc5b89dc0ad9b9fd4cbfe607b70c))
* clean old code spm list ([f349045](https://gitlab.com/maxxi-agro/atom/commit/f349045218b4c97ffb428879ed17b2dece24bc8b))
* clear log ([cd27f43](https://gitlab.com/maxxi-agro/atom/commit/cd27f433b4f41803065512bda70e574ef14ce83d))
* column product in detail so MTMAI-2163 ([6a112e7](https://gitlab.com/maxxi-agro/atom/commit/6a112e7ce77f924f69658bc86299cb5c832b35fd))
* Create SO payload set estimate_available to null if empty; MTMAI-1905 ([bdb18d9](https://gitlab.com/maxxi-agro/atom/commit/bdb18d96a94e9c69ffb3ae7699c28ca58419413b))
* create so; reset value on select change gudang ([c86a4d5](https://gitlab.com/maxxi-agro/atom/commit/c86a4d54795dc6e463a9d40ebea864c07ad2eb88))
* **Create-SO:** Modal after-create response button add "lihat detail" ([22489fc](https://gitlab.com/maxxi-agro/atom/commit/22489fc2b8595637fc3caaa86044239cef5162d4))
* create-spm show/hide available stock check with most request gudang ([65e6433](https://gitlab.com/maxxi-agro/atom/commit/65e64333b76839e82fc433443f4cd3ee4e95ba92))
* delete log ([4b81528](https://gitlab.com/maxxi-agro/atom/commit/4b81528324598c2e233261a90f3dec03a1fb3ac1))
* delete risk code ([0a85b37](https://gitlab.com/maxxi-agro/atom/commit/0a85b37fc3846bb4b8a2544ed5d60a1de961e8ff))
* design and redirect ([659a78f](https://gitlab.com/maxxi-agro/atom/commit/659a78fbb4fbca89149d1cceb52b706eb51cc736))
* detail distributor cbd MTMAI-1679 ([6cb4f3a](https://gitlab.com/maxxi-agro/atom/commit/6cb4f3a78d71162e67f9edac31700a93ae25775d))
* detail edit request distributor; handle deleted data branches MTMAI-1418 ([59928e0](https://gitlab.com/maxxi-agro/atom/commit/59928e02516a5979ce9294261d359f7475ae825e))
* detail edit request distributor; handle deleted data branches MTMAI-1418 ([190981f](https://gitlab.com/maxxi-agro/atom/commit/190981f2b06804cc8d4b275bd62593be4b185435))
* detail edit request finance; plafon kredit format rupiah to credit limit MTMAI-1270 ([28e4894](https://gitlab.com/maxxi-agro/atom/commit/28e48945ee64128234293ff262875672a215aad7))
* detail edit request finance; plafon kredit format rupiah to credit limit MTMAI-1270 ([ed4c180](https://gitlab.com/maxxi-agro/atom/commit/ed4c18035cf65bef52ae6ec61770d2d55219815a))
* detail edit request; map updated value of delivery address list MTMAI-1433 MTMAI-1434 ([82ab253](https://gitlab.com/maxxi-agro/atom/commit/82ab253d73ec094a897e8f8857ef166704c4822b))
* detail edit request; map updated value of delivery address list MTMAI-1433 MTMAI-1434 ([333ae3d](https://gitlab.com/maxxi-agro/atom/commit/333ae3d0a46e7795e3d43f2d23c51d4b3868be94))
* detail edit request; plafon kredit new value check for selling item list ([64bbedf](https://gitlab.com/maxxi-agro/atom/commit/64bbedf29d03c21bbd059187fe25eade53d78071))
* Detail sales order & Finance check; update  privilege approval handler ([14d2b73](https://gitlab.com/maxxi-agro/atom/commit/14d2b7388196388ae68868c3ec995acbe44c5f46))
* detail spm MTMAI-2085, MTMAI-2084 ([97c768d](https://gitlab.com/maxxi-agro/atom/commit/97c768df621a8e12daa96f5a5d46b00f8956dafb))
* distributor detail pending request; supplier sequence number MTMAI-1347 ([c52c053](https://gitlab.com/maxxi-agro/atom/commit/c52c0535ad5a4a4d905fe559a0292accf16f7f5f))
* distributor edit request delivery address dont map for null value ([54c0b15](https://gitlab.com/maxxi-agro/atom/commit/54c0b15e6c2fb62dc5512a7720cb060ca17555ce))
* distributor edit request; mapping update data cabang-usaha, supplier-information MTMAI-1433 MTMAI-1434 ([cc450ce](https://gitlab.com/maxxi-agro/atom/commit/cc450ce0374bde9a948c45201d0d9ab99fd4dcb5))
* distributor edit request; mapping update data cabang-usaha, supplier-information MTMAI-1433 MTMAI-1434 ([6b67a1d](https://gitlab.com/maxxi-agro/atom/commit/6b67a1d791b6651acca8d202e8aeb14c7bb6ba6b))
* Distributor log detail; noteview info render date value ([2b27a94](https://gitlab.com/maxxi-agro/atom/commit/2b27a9403bd7587ff54579f874bf2d1997f855d2))
* dokumen tab edit request; map from distributor_document_new_data MTMAI-1420 ([42fe188](https://gitlab.com/maxxi-agro/atom/commit/42fe188befe5c83c7698ce45fdb167b03abb2a53))
* **download qr batch:** Qr Produk detail handle error modal close before displaying response message ([25dbc26](https://gitlab.com/maxxi-agro/atom/commit/25dbc26c068049075ca6f7cedf2d5b4a5062892d))
* edit delivery distributor ([3902911](https://gitlab.com/maxxi-agro/atom/commit/39029116f632348b4079ddbafd6b65e5c551c293))
* enhance card detail body ([0d51f4c](https://gitlab.com/maxxi-agro/atom/commit/0d51f4cc1541c4582b0f6dc17cc3c0540fcf4cd5))
* enhance card header info ([ae67f19](https://gitlab.com/maxxi-agro/atom/commit/ae67f19a36adb95c0eb77050461f27302f3ad8c0))
* enhance print KUL distributor MTMAI-1542 ([502fb2c](https://gitlab.com/maxxi-agro/atom/commit/502fb2c359fa8c53f5a2121ac0a5dc57b48e530f))
* enhancement sales order MTMAI-1224 ([43c30b1](https://gitlab.com/maxxi-agro/atom/commit/43c30b187fa3d85d0792ee03b21d48525b27023b))
* filter chip PO and SO MTMAI-1909 ([1810c59](https://gitlab.com/maxxi-agro/atom/commit/1810c598bd3b26a17f23ee9de0b0378c0d2eb22e))
* filter date with max date range MTMAI-1901 ([2d4c489](https://gitlab.com/maxxi-agro/atom/commit/2d4c489c1218c5da607c8b6cefd6e4f28ff7b3b6))
* filter list SO MTMAI-1973 ([86fbf68](https://gitlab.com/maxxi-agro/atom/commit/86fbf68acb1fa22a59ae635c9bd0d33968b7ef5a))
* filter PO ([c7d3665](https://gitlab.com/maxxi-agro/atom/commit/c7d36651927f024c6bb0bb792dcf6664720b8edd))
* filter sales order ([b60c771](https://gitlab.com/maxxi-agro/atom/commit/b60c7711b6a3745076d04b405169282749a33cbf))
* filter so & modal save spm response MTMAI-2036, MTMAI-2082 ([0474764](https://gitlab.com/maxxi-agro/atom/commit/0474764435a39fc5ce1fb95dde02fa0c551650c0))
* filter spm ([492e909](https://gitlab.com/maxxi-agro/atom/commit/492e90983ea7e2f1ed709180c8842d9482390ab9))
* filter SPM and wording empty state MTMAI-2046 ([8c425f5](https://gitlab.com/maxxi-agro/atom/commit/8c425f5472872ad5fc0a22ead10db643706c5050))
* filter with search string MTMAI-1973 ([4ca2bf0](https://gitlab.com/maxxi-agro/atom/commit/4ca2bf00f0bc441effe49a59a149b176fc001526))
* **filter-list:** Add reset filter handle MTMAI-1891 ([d95e730](https://gitlab.com/maxxi-agro/atom/commit/d95e73035ee344e6d4a0a86080b298cd56b6bfd1))
* fix detail SO and create order service ([721ebef](https://gitlab.com/maxxi-agro/atom/commit/721ebefcd791a400354e5f4d2e016cda1b55ae25))
* fix percentage finance and pop up product spm MTMAI-2080, MTMAI-2079 ([ee79f9e](https://gitlab.com/maxxi-agro/atom/commit/ee79f9e93e915a82b8cc52f1a209accdb4cb1fe7))
* fix pop up simpan spm and filter status MTMAI-2056 ([1671862](https://gitlab.com/maxxi-agro/atom/commit/1671862a2785df19374b64de81aa3ae2a15abdaa))
* fix snackbar and refactor detail SO ([1acd043](https://gitlab.com/maxxi-agro/atom/commit/1acd043306f89671903d4b9284d6b3f90f9c8438))
* fix spm to detail in all status ([92b2b32](https://gitlab.com/maxxi-agro/atom/commit/92b2b32953661c8fd1abcb150123ffbb107356e2))
* form reject error control path field ([f9911ee](https://gitlab.com/maxxi-agro/atom/commit/f9911ee38216c2f318dc66e7e3de5686bc6d2c81))
* form reject error control path field ([a83bbad](https://gitlab.com/maxxi-agro/atom/commit/a83bbadb10503732f127b7535fa32d014bbde801))
* Handle response edit request approval MTMAI-1432 ([5f45f22](https://gitlab.com/maxxi-agro/atom/commit/5f45f22728a89981a28e0c254a48a1ae3e2ca1fc))
* Handle response edit request approval MTMAI-1432 ([85a314b](https://gitlab.com/maxxi-agro/atom/commit/85a314b8090d20c2fd2d0f7c2f887a239f656adf))
* handle tab SO MTMAI-2040, MTMAI-2032 ([76b48e6](https://gitlab.com/maxxi-agro/atom/commit/76b48e6077f8af0c0cdae8f37c31215dfa1d77ff))
* handle table column detail SO product ([477c880](https://gitlab.com/maxxi-agro/atom/commit/477c88031958362008cbeb6f88bab551796ace65))
* handle update value pinpoint - Alamat Pengiriman ([1e92db3](https://gitlab.com/maxxi-agro/atom/commit/1e92db330819d159d53898f9aa2233ef77ac24b9))
* hide btn modal approve spm ([61511e2](https://gitlab.com/maxxi-agro/atom/commit/61511e299e5205f31e0c46a4a8eba365b8de17ef))
* informasi pengelola usaha; add missing key value whatsapp_number MTMAI-1345 ([eca9f53](https://gitlab.com/maxxi-agro/atom/commit/eca9f53480fc38bd3e7f31a01e491e3109fbd7e1))
* informasi usaha sistem pembayaran key change to selling_system. MTMAI-1433 ([55b1660](https://gitlab.com/maxxi-agro/atom/commit/55b1660b8238bc88f3ae00715debae6d772976fa))
* **input-counter-order:** Handle gudang is most request; enable/disable input qty MTMAI-2087 ([9b6b997](https://gitlab.com/maxxi-agro/atom/commit/9b6b997da9feb1bbd5493e6e2bd114129173ee14))
* integration finance check so MTMAI-1524 ([cf44cf6](https://gitlab.com/maxxi-agro/atom/commit/cf44cf684539b0c5fea6c623a3ac5830edcb95b8))
* list blank after approve so MTMAI-1940 ([4368ae6](https://gitlab.com/maxxi-agro/atom/commit/4368ae65297520a8c977c1d5c10b910e0f8e2730))
* list SO status and filter MTMAI-1971 ([b6698c7](https://gitlab.com/maxxi-agro/atom/commit/b6698c7e0f370f6bb834e9a77f7ac603571cd95a))
* loading data detail SO ([8783c4e](https://gitlab.com/maxxi-agro/atom/commit/8783c4e73801951ef920a3d8a4820530983927a5))
* message title scan QR satuan MTMAI-1995 ([9cf36c8](https://gitlab.com/maxxi-agro/atom/commit/9cf36c82653873b53d166b39819e038bb020277f))
* modal reject retailer MTMAI-1506 ([f67bec7](https://gitlab.com/maxxi-agro/atom/commit/f67bec71a43473c3ba11aa998ce6874fa64ef0d7))
* **MTMAI-1272:** Distributor pending verification; handle get detail images document ([c505d55](https://gitlab.com/maxxi-agro/atom/commit/c505d555a32e899129eb9345e694ab95b6db1f88))
* **MTMAI-1422:** handle updated pinpoint map value for Alamat Pengiriman ([8299dc6](https://gitlab.com/maxxi-agro/atom/commit/8299dc6aaa94617ca1cc9afbd040c0db2f02ac39))
* **MTMAI-1891:** QR product list disable active filter on user search ([039fbcb](https://gitlab.com/maxxi-agro/atom/commit/039fbcb588e5416962923e8e2fecc368f0a57b2a))
* **MTMAI-2008:** handle item qty after sync list data ([5339557](https://gitlab.com/maxxi-agro/atom/commit/53395576cc45608a1315750fdf268c3b737b0430))
* **MTMAI-2013:** change endpoint get list product need-fullfill ([dde5a69](https://gitlab.com/maxxi-agro/atom/commit/dde5a69c88e51e8011cb2e76ecaa84b47d428507))
* **MTMAI-2022:** Approval Pusat; handle can approve so. refactor use detail data from service ([7101793](https://gitlab.com/maxxi-agro/atom/commit/71017939e1d9e5917ccff0e0ff8b6946e0cde6e5))
* **MTMAI-2028:** area warehouse checking response for assign gudang ([f78755d](https://gitlab.com/maxxi-agro/atom/commit/f78755d937cdf675e25bd9ef50a23518e0736c60))
* **MTMAI-2028:** Invalid noteview behavior; assign-gudang on detail PO with admin role ([60972bc](https://gitlab.com/maxxi-agro/atom/commit/60972bc824f855af1fcc860aebd9f295ea391924))
* **MTMAI-2047:** create spm - set invalid form condition one item with 0 value ([1105044](https://gitlab.com/maxxi-agro/atom/commit/110504402c72bab9ce849653f9b21bd6c90a56a7))
* **MTMAI-2047:** Create-spm; add card loader; form isValid condition check ([94294e8](https://gitlab.com/maxxi-agro/atom/commit/94294e86395c6de4512861a2265327b8a7011efd))
* **MTMAI-2047:** input-counter-order validation; create SPM - approval SO|SPM ([dca7c9e](https://gitlab.com/maxxi-agro/atom/commit/dca7c9ec8ffa14cf8c055b8878a3becbbfc028de))
* **MTMAI-2058:** validation form button create spm ([c3bb00d](https://gitlab.com/maxxi-agro/atom/commit/c3bb00d0566650c1c7549ec8185d5437804b292e))
* **MTMAI-2061:** Note view info change to blue icon ([09d5fd8](https://gitlab.com/maxxi-agro/atom/commit/09d5fd8c9dc889cef514e50f5bd229f2b78e8eb6))
* **MTMAI-2068:** Clear search keyword on input brand dialog close ([0c98843](https://gitlab.com/maxxi-agro/atom/commit/0c98843ebc5137dd2350b6c237a3cee94614a78f))
* **MTMAI-2071:** Create SO, revert validation qty inpuy; refactor ([d51156d](https://gitlab.com/maxxi-agro/atom/commit/d51156d572ce1104608c747cea75c3cadc33bc00))
* **MTMAI-2071:** Create-SO; validate product qty handle selected gudang is pusat ([4267aa0](https://gitlab.com/maxxi-agro/atom/commit/4267aa0f8733559ad9ef5f614c95b3ccea1b743a))
* **MTMAI-2074:** Approval-SO confirm dialog handle unmet qty value ([01afee2](https://gitlab.com/maxxi-agro/atom/commit/01afee2a78f3298592b4b7c3db108bfcabcd52af))
* **MTMAI-2074:** Approval-SO TAG; change key calculate unmet product qty ([ef55fe9](https://gitlab.com/maxxi-agro/atom/commit/ef55fe9aa69b021515c90d43b32267b1239c6a5b))
* **MTMAI-2086:** Approval SPM form button wording -> "Setujui SPM" ([f242b61](https://gitlab.com/maxxi-agro/atom/commit/f242b61183375bf55117a44ca4111754a8278662))
* **MTMAI-2086:** Page title wording typo -> "Surat Perintah Muat" ([0534216](https://gitlab.com/maxxi-agro/atom/commit/05342164b5e952ac950b40b9768310890df47f5d))
* **MTMAI-2097:** approval-so-tag confirmation wording ([fad5c7c](https://gitlab.com/maxxi-agro/atom/commit/fad5c7c0668e330215428d52c9738de485f5b70d))
* **MTMAI-2102:** Approval-SO handle negative value of qty_stock ([f7d8f3b](https://gitlab.com/maxxi-agro/atom/commit/f7d8f3b41c95c64a6de41473fdbc965967c00d48))
* **MTMAI-2108:** Detai SO, validate approval privilege for konfirmasi finance ([3a34f7a](https://gitlab.com/maxxi-agro/atom/commit/3a34f7ae15ae4857683f0f0ff1e4b57e059f211d))
* **MTMAI-2119 MTMAI-2120:** detail rejected noteview; modal reject revert changes button link ([fd86319](https://gitlab.com/maxxi-agro/atom/commit/fd86319941dd4df72eda69f36e0edfca09ef0f48))
* **MTMAI-2162:** SPM Detail Page- handle view cta: save spm; approval tag ([b67fe7c](https://gitlab.com/maxxi-agro/atom/commit/b67fe7c77a06e9e8ec469051c47291b6d2addbe7))
* **MTMAI-2224:** create SO - redirect url path to detail page ([63a9533](https://gitlab.com/maxxi-agro/atom/commit/63a9533afb46535defba60a2d32593daef48ac9d))
* **MTMAI-2224:** product item handle broken image ([309c582](https://gitlab.com/maxxi-agro/atom/commit/309c582187852a0a8802ff11b9750a5a32c5fb6c))
* **MTMAI-2236:** post register - add missing npwp payload ([fcc4a69](https://gitlab.com/maxxi-agro/atom/commit/fcc4a697d11aebfd078c0752d8748be390a7afc4))
* **MTMAI-2268:** product publish-request handle data edit ([101953c](https://gitlab.com/maxxi-agro/atom/commit/101953c9e2c6f6ce8a0c0089749e68f6da4a6f28))
* **MTMAI-2277:** detail retailer MAC change account_id key to mac_code ([b67c5fc](https://gitlab.com/maxxi-agro/atom/commit/b67c5fc0c394df7e66a3e292099b1d0ffbb16c65))
* **MTMAI-2277:** retailer MAC detail; user mitra id ([c9b1ca5](https://gitlab.com/maxxi-agro/atom/commit/c9b1ca5dfe5bcf5e94e95bcee82910bfaa084f86))
* no fill reason when is locked in detail spm ([63e5678](https://gitlab.com/maxxi-agro/atom/commit/63e5678c8cbd4d242ca1c0ebeca9e17b0e949f6c))
* no filtered by status for table qty process sales order MTMAI-2164 ([72c19c2](https://gitlab.com/maxxi-agro/atom/commit/72c19c28b8b80e37fa1b35c189c6a5283b66cdb8))
* note view after approve spm MTMAI-2105 ([2e8f838](https://gitlab.com/maxxi-agro/atom/commit/2e8f838413e0e3bc8b694aa40c5d3c4f35cd4cf4))
* note view save spm error condition ([150a277](https://gitlab.com/maxxi-agro/atom/commit/150a2771433a8ec0d81e89028635dd036efde6b1))
* noteview save spm ([d3a227b](https://gitlab.com/maxxi-agro/atom/commit/d3a227b4dd2e458ea365817d65b37bf59df37bf6))
* notification after reset one scan QR MTMAI-1995 ([b18fa06](https://gitlab.com/maxxi-agro/atom/commit/b18fa068d156f9e15edfd778e8218df9c74426b0))
* other product in spm detail sales order MTMAI-2101 ([61856f2](https://gitlab.com/maxxi-agro/atom/commit/61856f2ded5a45c3e1d2fc195fea8d644402464c))
* placeholder reject reason SO MTMAI-2118 ([68ea533](https://gitlab.com/maxxi-agro/atom/commit/68ea533adbfc02b9787b8acc71b48aafe5972eec))
* placeholder reject reason SO MTMAI-2118 ([6fb560f](https://gitlab.com/maxxi-agro/atom/commit/6fb560fbdc68af6566fd3efd30e67e5eb90952cf))
* plafon kredit render assurance value without type MTMAI-1539 ([c6b53a0](https://gitlab.com/maxxi-agro/atom/commit/c6b53a0a339af6780267fd196cfcece48048e160))
* **plafon kredit:** data plafon CBD should not have detail bills view MTMAI-1513 ([f02c4ec](https://gitlab.com/maxxi-agro/atom/commit/f02c4ecd9cb5e90c491ad79a42121fc279bb1472))
* price total create sales order MTMAI-2109 ([c2d02e2](https://gitlab.com/maxxi-agro/atom/commit/c2d02e2bcff34393ee17fb964bd585c373b4f9b6))
* privilege detail so MTMAI-2089 ([0715e37](https://gitlab.com/maxxi-agro/atom/commit/0715e37c868444a1314de54bf163eccdd13a8c53))
* product more spm list in detail so ([b7c787b](https://gitlab.com/maxxi-agro/atom/commit/b7c787bc4a15494aacfa8e8bae44c43dac127e58))
* product unscan tab MTMAI-2103 ([aa20594](https://gitlab.com/maxxi-agro/atom/commit/aa20594691f5c8788aba6bc3ff56d9fce356ab33))
* QR batch form; handle sync brand data MTMAI-1990 ([85d2bb8](https://gitlab.com/maxxi-agro/atom/commit/85d2bb815f21f2021d76b9b3ed72a07334416e04))
* **QR BATCH FORM:** Remove brand id from selected brand ([eb198e1](https://gitlab.com/maxxi-agro/atom/commit/eb198e1c33da1d754f1fb4aab9810acb9725fe2d))
* **qr-batch detail:** Note view render estimation date for on_progress qr batch MTMAI-1894 ([0a671ae](https://gitlab.com/maxxi-agro/atom/commit/0a671ae9946acb06b732bbfbebaf4655861a2529))
* **qr-batch detail:** tanggal dibuat format date MTMAI-1894 ([d3bf23f](https://gitlab.com/maxxi-agro/atom/commit/d3bf23f12886ef2b506240c86a727b8781906159))
* **qr-batch download:** add brand_id in qr-batch csv file MTMAI-2010 ([54adc7b](https://gitlab.com/maxxi-agro/atom/commit/54adc7bdbc67d1e20f631a88d8e0ef1796b69b5d))
* **qr-batch form:** Add confirmation dialog on cancel create batch MTMAI-1886 ([d4e7987](https://gitlab.com/maxxi-agro/atom/commit/d4e79870d7655b205ea96d840f8f82267ef8c4c2))
* **qr-batch form:** Add control name form error message MTMAI-1896 ([1e22f30](https://gitlab.com/maxxi-agro/atom/commit/1e22f3052d957b7fe9889742c70b642d21a4dccf))
* **qr-batch form:** Add form required validator ([2896649](https://gitlab.com/maxxi-agro/atom/commit/28966497472edf15fd52a016ff8062d4c1d94e20))
* **qr-batch form:** Input text render control error message MTMAI-1897 ([45ad99c](https://gitlab.com/maxxi-agro/atom/commit/45ad99cf45ea398ae72043240645b0c567873e06))
* **QR-Product:** QR list page empty state switch template ([57ffa1c](https://gitlab.com/maxxi-agro/atom/commit/57ffa1c3a5fb93df0706467f12ae2d928cc21995))
* reason reject finance MTMAI-2110 ([cd070d8](https://gitlab.com/maxxi-agro/atom/commit/cd070d8336ae83d6f4013699bc5d893a600d9bec))
* redirect after approve finance ([4602a72](https://gitlab.com/maxxi-agro/atom/commit/4602a727e5d910e5f07445cea7da21a9ff9c9de9))
* redirect detail breadcrumb spm ([0e2d3c5](https://gitlab.com/maxxi-agro/atom/commit/0e2d3c52dea229a066d9a6785160c96a1d843053))
* refresh data product in detail spm MTMAI-1977 ([cb8604a](https://gitlab.com/maxxi-agro/atom/commit/cb8604aeb8e11bf31d1c22cf25f2cc9b36b7b106))
* remove formatting date on document change data date; MTMAI-1377 ([080265c](https://gitlab.com/maxxi-agro/atom/commit/080265c5b2f690dad410127e383b6ad8ea8fdec5))
* remove temp. role privilege Qr Produk ([a0243ab](https://gitlab.com/maxxi-agro/atom/commit/a0243abce8fdc174b0c71b5d0db65ee51a5feebd))
* reset filter SO, PO & SPM MTMAI-2051 ([e3f4222](https://gitlab.com/maxxi-agro/atom/commit/e3f42221bffab65c709cd53cfd95788a3d37f374))
* revamp detail SO MTMAI-1526, MTMAI-1527 ([4bc1128](https://gitlab.com/maxxi-agro/atom/commit/4bc112820324728771820c2c7aa4e435a73726ac))
* revamp finance check in detail SO MTMAI-1525 ([ec3c4a2](https://gitlab.com/maxxi-agro/atom/commit/ec3c4a2f0d5520afee73cf781041ec2f4b0c9669))
* revamp PO and fix bug MTMAI-2062, MTMAI-2059, MTMAI-2041 ([c94cbde](https://gitlab.com/maxxi-agro/atom/commit/c94cbdef66b6c90eefc1981f969d59d7b8cb63cd))
* role privilege admin in detail spm MTMAI-2163 ([0002698](https://gitlab.com/maxxi-agro/atom/commit/0002698792ea0500b1839d89a799f119c29a67c3))
* show created date on tab SO in detail PO & fix design create SO MTMAI-2021, MTMAI-2020, MTMAI-2019 ([c8ce0c2](https://gitlab.com/maxxi-agro/atom/commit/c8ce0c22688c895f796e8dc695938fbe3dbf94a2))
* show sales order in detail PO ([e711acc](https://gitlab.com/maxxi-agro/atom/commit/e711acc3c91fd419f930186d99bf2df72e871c84))
* **SO Approval-TAG:** change payload key qty of sales_order_items_request MTMAI-2016 ([48d8aa5](https://gitlab.com/maxxi-agro/atom/commit/48d8aa59b70338c802ac8940b3096fcbf1bc6654))
* sorting PO MTMAI-1907, MTMAI-1906 ([9ef51ef](https://gitlab.com/maxxi-agro/atom/commit/9ef51ef6457a88805bebd4b5671af6176b36cd37))
* tab product unscan in detail so ([b8882bd](https://gitlab.com/maxxi-agro/atom/commit/b8882bd651c459b780b1aefaa7ba289a6b3b9a1a))
* table column and placeholder filter PO MTMAI-1913, MTMAI-1912, MTMAI-1911, MTMAI-1910, MTMAI-1908 ([dd0b2de](https://gitlab.com/maxxi-agro/atom/commit/dd0b2de08c2071a8567c801b14e90077786f5eeb))
* **table-data:** Add sorter column MTMAI-1989 ([d168cda](https://gitlab.com/maxxi-agro/atom/commit/d168cdacc33b8dfecce05e90a6d65f02f3bda7a7))
* **table-data:** QR product list add table sorter MTMAI-1989 ([60cd739](https://gitlab.com/maxxi-agro/atom/commit/60cd739bd277382320428afb953a4fa399207104))
* **table-pagination:** QR Batch product list; handle pagination MTMAI-1993 ([36c8075](https://gitlab.com/maxxi-agro/atom/commit/36c807576bcf4e9b611bda33ea79f9cc7a1a839b))
* total product list SO ([90d2c71](https://gitlab.com/maxxi-agro/atom/commit/90d2c71bf925ff48c2d47393b80d86bbb883373b))
* try again save SPM MTMAI-1975 ([29edcb8](https://gitlab.com/maxxi-agro/atom/commit/29edcb8472c5490f30a41736300505bf06e9b171))
* typo btn name ([24a187f](https://gitlab.com/maxxi-agro/atom/commit/24a187f6b0bda74d7f93db94b8a6d2a3fe6d3c93))
* validate btn create SO and modal product not full fill MTMAI-2011, MTMAI-2012 ([cc2abb4](https://gitlab.com/maxxi-agro/atom/commit/cc2abb4e48565382c96f84bbaac8e2ba94c134bc))
* validate create so if warehouse center MTMAI-1978 ([6f81318](https://gitlab.com/maxxi-agro/atom/commit/6f813181bd46c8a89b7fd5e236262bcfd77b7d27))
* validate max filter date SO and SPM MTMAI-2049 ([429702d](https://gitlab.com/maxxi-agro/atom/commit/429702d713f8d5d00ed7eae1add6cd361e8807c6))
* variant and sub area list in product exclusive ([ab38f69](https://gitlab.com/maxxi-agro/atom/commit/ab38f6979bd5510f46135b93022ef43eab637d60))
* warning price summary ([b7b775e](https://gitlab.com/maxxi-agro/atom/commit/b7b775e8d70e3f7b67a57c6691e10e58f9819652))
* wording and no validate btn unscan MTMAI-2002 ([3700d92](https://gitlab.com/maxxi-agro/atom/commit/3700d922bbbdc04ec791d41cb270e6db516b360f))
* wording empty PO MTMAI-1917 ([3b0d309](https://gitlab.com/maxxi-agro/atom/commit/3b0d30922470546de4d609a3cffecaa2c05878e9))
* wording note view modal approve MTMAI-2168 ([1f6ed00](https://gitlab.com/maxxi-agro/atom/commit/1f6ed00a9dc1de2d4d35993e315197833ee0a81b))
* wording note view modal approve MTMAI-2168 ([54feb52](https://gitlab.com/maxxi-agro/atom/commit/54feb52729d84f1a279cf618305bd4425d40cc69))

## [1.5.0](https://gitlab.com/maxxi-agro/atom/compare/v1.4.2...v2.0.0) (2023-09-29)


### ⚠ BREAKING CHANGES

* **Filter-Table:** Add input reset label on filter-table component MTMAI-1891

### Features

* add privelege menu spm ([0fcbbf9](https://gitlab.com/maxxi-agro/atom/commit/0fcbbf9d258ce0b6b0e5d4f3ddd35b34210bbc49))
* add privilege for tab product unscan so MTMAI-2169 ([54dea94](https://gitlab.com/maxxi-agro/atom/commit/54dea9416dec9ba45788947c144617dc5935d72f))
* add product unscan list MTMAI-1265 ([bc50586](https://gitlab.com/maxxi-agro/atom/commit/bc50586221354333f9c8869fcb9d528f2607acf3))
* add snackbar qr batch form create MTMAI-1215 ([2885a13](https://gitlab.com/maxxi-agro/atom/commit/2885a135f111ee91cf4ea612e1d3d0820c2d94e1))
* **approve-partial:** handle approval with reason form value MTMAI-1581 ([f54d36d](https://gitlab.com/maxxi-agro/atom/commit/f54d36df7950d584b3cc8c32e47ac2f085bedef9))
* checking if approve spm with complete scan product ([a45ec59](https://gitlab.com/maxxi-agro/atom/commit/a45ec5986eaaa17a3c48284f569d640d26df9e61))
* component card header information ([01990f4](https://gitlab.com/maxxi-agro/atom/commit/01990f41b3e089d029855844b4987fe6babac043))
* component note view with detail ([46e5e90](https://gitlab.com/maxxi-agro/atom/commit/46e5e90c1e75007540a6fa93507b23a61f6f71a3))
* counting list SPM, SO, PO MTMAI-1903 ([098a42f](https://gitlab.com/maxxi-agro/atom/commit/098a42fefabcc600811b5d5d9d863625447b8bc7))
* create spm page component; MTMAI-1186 ([851fb43](https://gitlab.com/maxxi-agro/atom/commit/851fb43a31ae9c441c042dfe7eb13d98bb1e099f))
* detail process scan MTMAI-1190, MTMAI-1308, MTMAI-1309 ([f924b29](https://gitlab.com/maxxi-agro/atom/commit/f924b29c10341bddc316fc14156b0982fefc2e51))
* Detail QR Batch page; add modal confirm/response dialog. Integrate endpoint download;  MTMAI-1221 MTMAI-1315 ([77b524f](https://gitlab.com/maxxi-agro/atom/commit/77b524f4b509ae533e90983a0775e0dd196cfd67))
* Detail QR batch page; batch info card MTMAI-1221 ([10fcc1c](https://gitlab.com/maxxi-agro/atom/commit/10fcc1cc6febcb77b2b19560de79fb488b72f5eb))
* detail reset product scan SPM MTMAI-1191, MTMAI-1301 ([d95581e](https://gitlab.com/maxxi-agro/atom/commit/d95581e54dcea19ed0d312dc90f6afcec7a99154))
* detail so approval hq ([a7ef666](https://gitlab.com/maxxi-agro/atom/commit/a7ef666b07fd1c9246a0d546738ba44b2d7ec418))
* **detail-retailer:** enhancement informasi kemitraan card; add NPWP value for R1 ([ef22859](https://gitlab.com/maxxi-agro/atom/commit/ef22859039ebb5d604c411aa52691a9749f4e93a))
* enhance detail SO MTMAI-1265, MTMAI-1289, MTMAI-1290, MTMAI-1291, MTMAI-1292, MTMAI-1293, MTMAI-1294 ([25e7cdb](https://gitlab.com/maxxi-agro/atom/commit/25e7cdb1fd25b737568aa8dc8fad18a05a3bdcdc))
* enhance detail spm MTMAI-1595, MTMAI-1596, MTMAI-1597 ([d789262](https://gitlab.com/maxxi-agro/atom/commit/d789262c414e5c460b1456416d0751b0595cf6c5))
* enhance SO detail and spm MTMAI-1557, MTMAI-1558, MTMAI-1559, MTMAI-1560, MTMAI-1584, MTMAI-1587, MTMAI-1611 ([812a811](https://gitlab.com/maxxi-agro/atom/commit/812a81129852bb84fab3573ce3068c86e43d60ef))
* enhance so sedang menunggu MTMAI-1578, MTMAI-1580, MTMAI-1609, MTMAI-1610 ([277d85e](https://gitlab.com/maxxi-agro/atom/commit/277d85e504b6eebb22b8b2a8e68ae21398417848))
* **Filter-Table:** Add input reset label on filter-table component MTMAI-1891 ([45e31e8](https://gitlab.com/maxxi-agro/atom/commit/45e31e8645de3b11a25497e4317006acc3e93d57))
* Form add qr batch wip; add component and page ([a93c522](https://gitlab.com/maxxi-agro/atom/commit/a93c522d62da3f23b20dd1b5c730093fab5cf63c))
* handle create qr batch form; post and get response wip MTMAI-1210 MTMAI-1211 MTMAI-1213 MTMAI-1214 MTMAI-1314 ([40ec5cb](https://gitlab.com/maxxi-agro/atom/commit/40ec5cbde4214a6b7fd4fc3d8d4bc6c9afa599b1))
* handle link cancel/create spm page; MTMAI-1186 ([d72b1ea](https://gitlab.com/maxxi-agro/atom/commit/d72b1eac2e91de2a520868197db98fa7b6b15943))
* **handle unmet product:** Approval so-tag form modal reason MTMAI-1588 ([195e0a8](https://gitlab.com/maxxi-agro/atom/commit/195e0a8b484637b304cf4ba1e0b411657eed9392))
* **info-card:** Retailer detail MAC user informasi kemitraan MTMAI-1569 ([7e3919a](https://gitlab.com/maxxi-agro/atom/commit/7e3919a30f696bed5c102b41aaa11e39487f20ee))
* integrate noteview spm if status selesai sebagian SPM MTMAI-1591 ([5da8616](https://gitlab.com/maxxi-agro/atom/commit/5da861629aa8666a0a9482bd522c9f31e70e37be))
* **integrate-api:** add confirm modal; reason modal; handle post approval MTMAI-1599 MTMAI-1600 MTMAI-1601 ([4bcf8ae](https://gitlab.com/maxxi-agro/atom/commit/4bcf8ae23dc91179518c6cc9015f56bc40b40c1c))
* **integrate-api:** approval so-tag map product order data; MTMAI-1583 ([a196899](https://gitlab.com/maxxi-agro/atom/commit/a1968998eeee328f65913a26fe44589e71b50365))
* **integrate-API:** approvall SO-TAG handle reject/approve MTMAI-1583 ([d678f30](https://gitlab.com/maxxi-agro/atom/commit/d678f30e2a9434da5538c6002d4d2bc4d69f7be3))
* integration menunggu konfirmasi spm and fix detail spm breadcrumb MTMAI-1594 ([ef1126d](https://gitlab.com/maxxi-agro/atom/commit/ef1126db075bf7b337a0bdd1a6e79f2b9f8f13d3))
* integration price summary MTMAI-1527 ([7b18a05](https://gitlab.com/maxxi-agro/atom/commit/7b18a05c132c5a4204d514258162f72283d9ff0e))
* intigration privilege detail spm ([c64b330](https://gitlab.com/maxxi-agro/atom/commit/c64b330c282d9aa0d5a2680e84a8ce26a8b9a4f5))
* **listbox-filter:** Add filter by key property ([d44e6af](https://gitlab.com/maxxi-agro/atom/commit/d44e6af49dd9dfe2c7602487146fe1c606112906))
* **MAC-Registration:** Add confirm/response dialog; handle success MTMAI-1572 MTMAI-1608 ([f35bedc](https://gitlab.com/maxxi-agro/atom/commit/f35bedc56f42b26fac0eb49887e89d66003d8687))
* **MTMAI-1566:** List retailer enhance filter for MAC mitra MTMAI-1568 ([48abcbf](https://gitlab.com/maxxi-agro/atom/commit/48abcbf22336678121b0deab9315fdeccf011742))
* post create batch qr; MTMAI-1210 ([784fa5f](https://gitlab.com/maxxi-agro/atom/commit/784fa5f89b8c19aec44a8399040dd8c7d90eaf93))
* **price summary:** Render price summary card MTMAI-1583 ([6f09cc9](https://gitlab.com/maxxi-agro/atom/commit/6f09cc953788fc24f90f1cba49e8ae68c24149b0))
* privilege btn save spm for admin ([484f02f](https://gitlab.com/maxxi-agro/atom/commit/484f02f454bf2579ad00597e426a62aac7d1acb0))
* privilege tab SO pending ([7237900](https://gitlab.com/maxxi-agro/atom/commit/7237900efdf7dd0a313d3f21c4fe5baad60987f0))
* qr batch create form; cancel remove item handler MTMAI-1215 ([268cfb5](https://gitlab.com/maxxi-agro/atom/commit/268cfb5e3cd6093ebc719e4831dfad49d1ff13a6))
* QR batch detail get list produk wip ([c0cf63c](https://gitlab.com/maxxi-agro/atom/commit/c0cf63c663f8a67284b6722cf46255104f75ee82))
* qr batch form create; handle value current-selected brand on add/remove MTMAI-1215 ([b5d2c49](https://gitlab.com/maxxi-agro/atom/commit/b5d2c493557a22c72536afe6cc14aee523adf453))
* QR Product list close modal response on error data; MTMAI-1208 ([e01e9c7](https://gitlab.com/maxxi-agro/atom/commit/e01e9c73fa5132c111f128c607cb37ed5f77ddcd))
* QR Product list close modal response on error data; MTMAI-1208 ([2b97f44](https://gitlab.com/maxxi-agro/atom/commit/2b97f440bd7de38afbc866bb6ae8e4d96ee7de0c))
* QR Product list, page filter & search MTMAI-1208 ([c13c2eb](https://gitlab.com/maxxi-agro/atom/commit/c13c2ebff0eccf88766e3a64f35385f7f03968e6))
* QR Product list, page filter & search MTMAI-1208 ([4fd68c2](https://gitlab.com/maxxi-agro/atom/commit/4fd68c29fa0233e6e53dc68db0b0e525e6f6ef9c))
* QR produk list add batch download modal confirm/response MTMAI-1208 ([2f996fe](https://gitlab.com/maxxi-agro/atom/commit/2f996fe4ed17c914f2e69a4b8df627c73f9e7ee0))
* QR produk list add batch download modal confirm/response MTMAI-1208 ([255156c](https://gitlab.com/maxxi-agro/atom/commit/255156c2ff2708a94ca12241a49daa124b09d5e1))
* **qr-batch form:** Add limit qty(5500) input per-batch; Add limit(5) select brand per-batch ([7a2dafa](https://gitlab.com/maxxi-agro/atom/commit/7a2dafa8e39078f5801c605c94fd4e28ceabaca1))
* **re-map data detail so spm:** handle post form create spm MTMAI-1300 ([67456a4](https://gitlab.com/maxxi-agro/atom/commit/67456a412fdfe3343b8b183783fbe58d84379c84))
* **register-mac form:** Add Input cakupan wilayah MTMAI-1572 ([23847b5](https://gitlab.com/maxxi-agro/atom/commit/23847b5b27966a79b147b893f6fa142a434538f5))
* **register-mac form:** render form data MTMAI-1572 ([2a00c77](https://gitlab.com/maxxi-agro/atom/commit/2a00c77055b8d17dd69ab18ba46fd11f2769eab3))
* **reject so-tag:** add  reject so-tag modal MTMAI-1581 ([c51d9ea](https://gitlab.com/maxxi-agro/atom/commit/c51d9eab1912d2480d5bb806de99aee604a1a8f0))
* **reject SPM TAG:** add reject dialog; form reason dialog; handle post reject spm-tag MTMAI-1592 MTMAI-1599 ([39687fd](https://gitlab.com/maxxi-agro/atom/commit/39687fddbdb4f225523939074b916efda700fb28))
* **reject-spm:** modal reject form reject handler MTMAI-1599 ([f3fa6f5](https://gitlab.com/maxxi-agro/atom/commit/f3fa6f5db17449b6b10b04e85a1f2b925ec7c1de))
* **retailer detail:** add badge MAC; add menu register MAC MTMAI-1569 ([87a71da](https://gitlab.com/maxxi-agro/atom/commit/87a71da0d3302afdec052534d697aa824ed7d0b1))
* setup qr batch form, add input counter, add modal brand list, render table list MTMAI-1210 ([3d8fad8](https://gitlab.com/maxxi-agro/atom/commit/3d8fad8e94460f0d27c1f2e129ca46bc409cfd95))
* spm form create render get data; wip MTMAI-1186 ([32ef1fe](https://gitlab.com/maxxi-agro/atom/commit/32ef1fe690fa920b065cec7f1b1aebb5fc4bce54))
* SPM list & detail MTMAI-1183 ([6686623](https://gitlab.com/maxxi-agro/atom/commit/6686623eab50da00de30e469a13bbf2f5ef6697a))
* spm list in detail SO ([5ff10b3](https://gitlab.com/maxxi-agro/atom/commit/5ff10b3d2bc8a32898b31724e83da81373a933b4))
* spm menunggu konfirmasi list MTMAI-1590, MTMAI-1593 ([320b504](https://gitlab.com/maxxi-agro/atom/commit/320b504dcf942c28709a67b9e805b782703b9ce3))
* tab child component and fix load detail api ([80dfccb](https://gitlab.com/maxxi-agro/atom/commit/80dfccbf209646b43ef0a527336a915159efea5d))
* tab DO in detail spm and redirect after save spm ([b2a9255](https://gitlab.com/maxxi-agro/atom/commit/b2a9255eda854b284a63a5f8ad5790a34fa7bc3c))
* **tab-component:** make tab more usable ([adf94ef](https://gitlab.com/maxxi-agro/atom/commit/adf94ef6f7c0359e1b12896249767d020d3401b5))
* **tab-component:** Tab container use componentOutlet to load child component ([9f003d0](https://gitlab.com/maxxi-agro/atom/commit/9f003d09e529f45d71c580c78392b6c40830979b))
* **table data list:** Add table list; column kemitraan MTMAI-1567 ([d868515](https://gitlab.com/maxxi-agro/atom/commit/d868515fb50ddcba0016f2ab297a8523c8d541c7))
* **ui-slicing:** add approval sales-order TAG MTMAI-1582 ([b7ddaec](https://gitlab.com/maxxi-agro/atom/commit/b7ddaecc40100a1aa3d13bc2eda2a319d49ca596))
* **UI:** Add component page MAC Registration MTMAI-1572 ([bc2ea3a](https://gitlab.com/maxxi-agro/atom/commit/bc2ea3ac8a82dd6ac71869dff3888e41300ddc4a))
* **ui:** Approval TAG add section information component; init get form data MTMAI-1598 ([3e49418](https://gitlab.com/maxxi-agro/atom/commit/3e49418aa8c1553586b4ce81f42e715460a2833e))


### Bug Fixes

*  mapping updated value of target penjualan - penagihan kredit; modal detail piutang MTMAI-1510 MTMAI-1513 ([70ef682](https://gitlab.com/maxxi-agro/atom/commit/70ef6825880663e595d01904471ced6d179babba))
*  render edited value data, cabang-usaha|informasi-supplier MTMAI-1433 MTMAI-1434 ([058bd94](https://gitlab.com/maxxi-agro/atom/commit/058bd94e9109d278bf975a693427914f16fcdbf5))
*  render edited value data, cabang-usaha|informasi-supplier MTMAI-1433 MTMAI-1434 ([2aeb0a8](https://gitlab.com/maxxi-agro/atom/commit/2aeb0a8ca0c6bcb9f282a3eb9613468614430388))
* add empty state if no dokumen pembaruan data; edit finance request MTMAI-1516 ([18fa69b](https://gitlab.com/maxxi-agro/atom/commit/18fa69b10294492cc89ade34c720438590bd628b))
* add summary counting SPM and SO MTMAI-2038, MTMAI-2037 ([cf0840d](https://gitlab.com/maxxi-agro/atom/commit/cf0840d6640cd4b6ab2e73c1a0c24b3f0ac73231))
* alamat pengiriman loading card MTMAI-1538 ([040e5d3](https://gitlab.com/maxxi-agro/atom/commit/040e5d33e46cd337b78ab354742c972eeb062fa1))
* alamat pengiriman mapping data fix MTMAI-1516 ([c292684](https://gitlab.com/maxxi-agro/atom/commit/c292684eb2b206bdd22141efe32f6de9fe73f375))
* **Approval-SO:** handle calculate subtotal-price product with discount ([d40bd60](https://gitlab.com/maxxi-agro/atom/commit/d40bd6001f2bdd14e7c0558e40009af811a860b7))
* **approval-tag:** post approval so/spm payload changes ([4bbdcc7](https://gitlab.com/maxxi-agro/atom/commit/4bbdcc7a1589ffa60f2e64e2ab6c88fb0ed9f878))
* assign warehouse in detail po need full fill ([be67de0](https://gitlab.com/maxxi-agro/atom/commit/be67de0708f1358ad1cfc9e1bce73c1400ba3769))
* breadcrumb detail spm ([49a81c1](https://gitlab.com/maxxi-agro/atom/commit/49a81c1871905c5170d9195df8e333484258e393))
* btn approve center spm ([2eb2df1](https://gitlab.com/maxxi-agro/atom/commit/2eb2df17b7e872f247d2c168702e9cfa5de31686))
* btn save spm modal ([2a06ffd](https://gitlab.com/maxxi-agro/atom/commit/2a06ffde376334ce76f9a3a891ac6b40f5294169))
* **Card header:** Remove unwanted wording ([ce24258](https://gitlab.com/maxxi-agro/atom/commit/ce2425865d8443ae9050b1f4220c59c32bf22197))
* change api approve finance ([59d2c6a](https://gitlab.com/maxxi-agro/atom/commit/59d2c6a3fbe6fc5b89dc0ad9b9fd4cbfe607b70c))
* clean old code spm list ([f349045](https://gitlab.com/maxxi-agro/atom/commit/f349045218b4c97ffb428879ed17b2dece24bc8b))
* column product in detail so MTMAI-2163 ([6a112e7](https://gitlab.com/maxxi-agro/atom/commit/6a112e7ce77f924f69658bc86299cb5c832b35fd))
* Create SO payload set estimate_available to null if empty; MTMAI-1905 ([bdb18d9](https://gitlab.com/maxxi-agro/atom/commit/bdb18d96a94e9c69ffb3ae7699c28ca58419413b))
* create so; reset value on select change gudang ([c86a4d5](https://gitlab.com/maxxi-agro/atom/commit/c86a4d54795dc6e463a9d40ebea864c07ad2eb88))
* **Create-SO:** Modal after-create response button add "lihat detail" ([22489fc](https://gitlab.com/maxxi-agro/atom/commit/22489fc2b8595637fc3caaa86044239cef5162d4))
* create-spm show/hide available stock check with most request gudang ([65e6433](https://gitlab.com/maxxi-agro/atom/commit/65e64333b76839e82fc433443f4cd3ee4e95ba92))
* delete log ([4b81528](https://gitlab.com/maxxi-agro/atom/commit/4b81528324598c2e233261a90f3dec03a1fb3ac1))
* delete risk code ([0a85b37](https://gitlab.com/maxxi-agro/atom/commit/0a85b37fc3846bb4b8a2544ed5d60a1de961e8ff))
* design and redirect ([659a78f](https://gitlab.com/maxxi-agro/atom/commit/659a78fbb4fbca89149d1cceb52b706eb51cc736))
* detail distributor cbd MTMAI-1679 ([6cb4f3a](https://gitlab.com/maxxi-agro/atom/commit/6cb4f3a78d71162e67f9edac31700a93ae25775d))
* detail edit request distributor; handle deleted data branches MTMAI-1418 ([59928e0](https://gitlab.com/maxxi-agro/atom/commit/59928e02516a5979ce9294261d359f7475ae825e))
* detail edit request distributor; handle deleted data branches MTMAI-1418 ([190981f](https://gitlab.com/maxxi-agro/atom/commit/190981f2b06804cc8d4b275bd62593be4b185435))
* detail edit request finance; plafon kredit format rupiah to credit limit MTMAI-1270 ([28e4894](https://gitlab.com/maxxi-agro/atom/commit/28e48945ee64128234293ff262875672a215aad7))
* detail edit request finance; plafon kredit format rupiah to credit limit MTMAI-1270 ([ed4c180](https://gitlab.com/maxxi-agro/atom/commit/ed4c18035cf65bef52ae6ec61770d2d55219815a))
* detail edit request; map updated value of delivery address list MTMAI-1433 MTMAI-1434 ([82ab253](https://gitlab.com/maxxi-agro/atom/commit/82ab253d73ec094a897e8f8857ef166704c4822b))
* detail edit request; map updated value of delivery address list MTMAI-1433 MTMAI-1434 ([333ae3d](https://gitlab.com/maxxi-agro/atom/commit/333ae3d0a46e7795e3d43f2d23c51d4b3868be94))
* detail edit request; plafon kredit new value check for selling item list ([64bbedf](https://gitlab.com/maxxi-agro/atom/commit/64bbedf29d03c21bbd059187fe25eade53d78071))
* Detail sales order & Finance check; update  privilege approval handler ([14d2b73](https://gitlab.com/maxxi-agro/atom/commit/14d2b7388196388ae68868c3ec995acbe44c5f46))
* detail spm MTMAI-2085, MTMAI-2084 ([97c768d](https://gitlab.com/maxxi-agro/atom/commit/97c768df621a8e12daa96f5a5d46b00f8956dafb))
* distributor detail pending request; supplier sequence number MTMAI-1347 ([c52c053](https://gitlab.com/maxxi-agro/atom/commit/c52c0535ad5a4a4d905fe559a0292accf16f7f5f))
* distributor edit request delivery address dont map for null value ([54c0b15](https://gitlab.com/maxxi-agro/atom/commit/54c0b15e6c2fb62dc5512a7720cb060ca17555ce))
* distributor edit request; mapping update data cabang-usaha, supplier-information MTMAI-1433 MTMAI-1434 ([cc450ce](https://gitlab.com/maxxi-agro/atom/commit/cc450ce0374bde9a948c45201d0d9ab99fd4dcb5))
* distributor edit request; mapping update data cabang-usaha, supplier-information MTMAI-1433 MTMAI-1434 ([6b67a1d](https://gitlab.com/maxxi-agro/atom/commit/6b67a1d791b6651acca8d202e8aeb14c7bb6ba6b))
* Distributor log detail; noteview info render date value ([2b27a94](https://gitlab.com/maxxi-agro/atom/commit/2b27a9403bd7587ff54579f874bf2d1997f855d2))
* dokumen tab edit request; map from distributor_document_new_data MTMAI-1420 ([42fe188](https://gitlab.com/maxxi-agro/atom/commit/42fe188befe5c83c7698ce45fdb167b03abb2a53))
* **download qr batch:** Qr Produk detail handle error modal close before displaying response message ([25dbc26](https://gitlab.com/maxxi-agro/atom/commit/25dbc26c068049075ca6f7cedf2d5b4a5062892d))
* edit delivery distributor ([3902911](https://gitlab.com/maxxi-agro/atom/commit/39029116f632348b4079ddbafd6b65e5c551c293))
* enhance card detail body ([0d51f4c](https://gitlab.com/maxxi-agro/atom/commit/0d51f4cc1541c4582b0f6dc17cc3c0540fcf4cd5))
* enhance card header info ([ae67f19](https://gitlab.com/maxxi-agro/atom/commit/ae67f19a36adb95c0eb77050461f27302f3ad8c0))
* enhance print KUL distributor MTMAI-1542 ([502fb2c](https://gitlab.com/maxxi-agro/atom/commit/502fb2c359fa8c53f5a2121ac0a5dc57b48e530f))
* enhancement sales order MTMAI-1224 ([43c30b1](https://gitlab.com/maxxi-agro/atom/commit/43c30b187fa3d85d0792ee03b21d48525b27023b))
* filter chip PO and SO MTMAI-1909 ([1810c59](https://gitlab.com/maxxi-agro/atom/commit/1810c598bd3b26a17f23ee9de0b0378c0d2eb22e))
* filter date with max date range MTMAI-1901 ([2d4c489](https://gitlab.com/maxxi-agro/atom/commit/2d4c489c1218c5da607c8b6cefd6e4f28ff7b3b6))
* filter list SO MTMAI-1973 ([86fbf68](https://gitlab.com/maxxi-agro/atom/commit/86fbf68acb1fa22a59ae635c9bd0d33968b7ef5a))
* filter PO ([c7d3665](https://gitlab.com/maxxi-agro/atom/commit/c7d36651927f024c6bb0bb792dcf6664720b8edd))
* filter sales order ([b60c771](https://gitlab.com/maxxi-agro/atom/commit/b60c7711b6a3745076d04b405169282749a33cbf))
* filter so & modal save spm response MTMAI-2036, MTMAI-2082 ([0474764](https://gitlab.com/maxxi-agro/atom/commit/0474764435a39fc5ce1fb95dde02fa0c551650c0))
* filter spm ([492e909](https://gitlab.com/maxxi-agro/atom/commit/492e90983ea7e2f1ed709180c8842d9482390ab9))
* filter SPM and wording empty state MTMAI-2046 ([8c425f5](https://gitlab.com/maxxi-agro/atom/commit/8c425f5472872ad5fc0a22ead10db643706c5050))
* filter with search string MTMAI-1973 ([4ca2bf0](https://gitlab.com/maxxi-agro/atom/commit/4ca2bf00f0bc441effe49a59a149b176fc001526))
* **filter-list:** Add reset filter handle MTMAI-1891 ([d95e730](https://gitlab.com/maxxi-agro/atom/commit/d95e73035ee344e6d4a0a86080b298cd56b6bfd1))
* fix detail SO and create order service ([721ebef](https://gitlab.com/maxxi-agro/atom/commit/721ebefcd791a400354e5f4d2e016cda1b55ae25))
* fix percentage finance and pop up product spm MTMAI-2080, MTMAI-2079 ([ee79f9e](https://gitlab.com/maxxi-agro/atom/commit/ee79f9e93e915a82b8cc52f1a209accdb4cb1fe7))
* fix pop up simpan spm and filter status MTMAI-2056 ([1671862](https://gitlab.com/maxxi-agro/atom/commit/1671862a2785df19374b64de81aa3ae2a15abdaa))
* fix snackbar and refactor detail SO ([1acd043](https://gitlab.com/maxxi-agro/atom/commit/1acd043306f89671903d4b9284d6b3f90f9c8438))
* fix spm to detail in all status ([92b2b32](https://gitlab.com/maxxi-agro/atom/commit/92b2b32953661c8fd1abcb150123ffbb107356e2))
* form reject error control path field ([f9911ee](https://gitlab.com/maxxi-agro/atom/commit/f9911ee38216c2f318dc66e7e3de5686bc6d2c81))
* form reject error control path field ([a83bbad](https://gitlab.com/maxxi-agro/atom/commit/a83bbadb10503732f127b7535fa32d014bbde801))
* Handle response edit request approval MTMAI-1432 ([5f45f22](https://gitlab.com/maxxi-agro/atom/commit/5f45f22728a89981a28e0c254a48a1ae3e2ca1fc))
* Handle response edit request approval MTMAI-1432 ([85a314b](https://gitlab.com/maxxi-agro/atom/commit/85a314b8090d20c2fd2d0f7c2f887a239f656adf))
* handle tab SO MTMAI-2040, MTMAI-2032 ([76b48e6](https://gitlab.com/maxxi-agro/atom/commit/76b48e6077f8af0c0cdae8f37c31215dfa1d77ff))
* handle table column detail SO product ([477c880](https://gitlab.com/maxxi-agro/atom/commit/477c88031958362008cbeb6f88bab551796ace65))
* handle update value pinpoint - Alamat Pengiriman ([1e92db3](https://gitlab.com/maxxi-agro/atom/commit/1e92db330819d159d53898f9aa2233ef77ac24b9))
* hide btn modal approve spm ([61511e2](https://gitlab.com/maxxi-agro/atom/commit/61511e299e5205f31e0c46a4a8eba365b8de17ef))
* informasi pengelola usaha; add missing key value whatsapp_number MTMAI-1345 ([eca9f53](https://gitlab.com/maxxi-agro/atom/commit/eca9f53480fc38bd3e7f31a01e491e3109fbd7e1))
* informasi usaha sistem pembayaran key change to selling_system. MTMAI-1433 ([55b1660](https://gitlab.com/maxxi-agro/atom/commit/55b1660b8238bc88f3ae00715debae6d772976fa))
* **input-counter-order:** Handle gudang is most request; enable/disable input qty MTMAI-2087 ([9b6b997](https://gitlab.com/maxxi-agro/atom/commit/9b6b997da9feb1bbd5493e6e2bd114129173ee14))
* integration finance check so MTMAI-1524 ([cf44cf6](https://gitlab.com/maxxi-agro/atom/commit/cf44cf684539b0c5fea6c623a3ac5830edcb95b8))
* list blank after approve so MTMAI-1940 ([4368ae6](https://gitlab.com/maxxi-agro/atom/commit/4368ae65297520a8c977c1d5c10b910e0f8e2730))
* list SO status and filter MTMAI-1971 ([b6698c7](https://gitlab.com/maxxi-agro/atom/commit/b6698c7e0f370f6bb834e9a77f7ac603571cd95a))
* loading data detail SO ([8783c4e](https://gitlab.com/maxxi-agro/atom/commit/8783c4e73801951ef920a3d8a4820530983927a5))
* message title scan QR satuan MTMAI-1995 ([9cf36c8](https://gitlab.com/maxxi-agro/atom/commit/9cf36c82653873b53d166b39819e038bb020277f))
* modal reject retailer MTMAI-1506 ([f67bec7](https://gitlab.com/maxxi-agro/atom/commit/f67bec71a43473c3ba11aa998ce6874fa64ef0d7))
* **MTMAI-1272:** Distributor pending verification; handle get detail images document ([c505d55](https://gitlab.com/maxxi-agro/atom/commit/c505d555a32e899129eb9345e694ab95b6db1f88))
* **MTMAI-1422:** handle updated pinpoint map value for Alamat Pengiriman ([8299dc6](https://gitlab.com/maxxi-agro/atom/commit/8299dc6aaa94617ca1cc9afbd040c0db2f02ac39))
* **MTMAI-1891:** QR product list disable active filter on user search ([039fbcb](https://gitlab.com/maxxi-agro/atom/commit/039fbcb588e5416962923e8e2fecc368f0a57b2a))
* **MTMAI-2008:** handle item qty after sync list data ([5339557](https://gitlab.com/maxxi-agro/atom/commit/53395576cc45608a1315750fdf268c3b737b0430))
* **MTMAI-2013:** change endpoint get list product need-fullfill ([dde5a69](https://gitlab.com/maxxi-agro/atom/commit/dde5a69c88e51e8011cb2e76ecaa84b47d428507))
* **MTMAI-2022:** Approval Pusat; handle can approve so. refactor use detail data from service ([7101793](https://gitlab.com/maxxi-agro/atom/commit/71017939e1d9e5917ccff0e0ff8b6946e0cde6e5))
* **MTMAI-2028:** area warehouse checking response for assign gudang ([f78755d](https://gitlab.com/maxxi-agro/atom/commit/f78755d937cdf675e25bd9ef50a23518e0736c60))
* **MTMAI-2028:** Invalid noteview behavior; assign-gudang on detail PO with admin role ([60972bc](https://gitlab.com/maxxi-agro/atom/commit/60972bc824f855af1fcc860aebd9f295ea391924))
* **MTMAI-2047:** create spm - set invalid form condition one item with 0 value ([1105044](https://gitlab.com/maxxi-agro/atom/commit/110504402c72bab9ce849653f9b21bd6c90a56a7))
* **MTMAI-2047:** Create-spm; add card loader; form isValid condition check ([94294e8](https://gitlab.com/maxxi-agro/atom/commit/94294e86395c6de4512861a2265327b8a7011efd))
* **MTMAI-2047:** input-counter-order validation; create SPM - approval SO|SPM ([dca7c9e](https://gitlab.com/maxxi-agro/atom/commit/dca7c9ec8ffa14cf8c055b8878a3becbbfc028de))
* **MTMAI-2058:** validation form button create spm ([c3bb00d](https://gitlab.com/maxxi-agro/atom/commit/c3bb00d0566650c1c7549ec8185d5437804b292e))
* **MTMAI-2061:** Note view info change to blue icon ([09d5fd8](https://gitlab.com/maxxi-agro/atom/commit/09d5fd8c9dc889cef514e50f5bd229f2b78e8eb6))
* **MTMAI-2068:** Clear search keyword on input brand dialog close ([0c98843](https://gitlab.com/maxxi-agro/atom/commit/0c98843ebc5137dd2350b6c237a3cee94614a78f))
* **MTMAI-2071:** Create SO, revert validation qty inpuy; refactor ([d51156d](https://gitlab.com/maxxi-agro/atom/commit/d51156d572ce1104608c747cea75c3cadc33bc00))
* **MTMAI-2071:** Create-SO; validate product qty handle selected gudang is pusat ([4267aa0](https://gitlab.com/maxxi-agro/atom/commit/4267aa0f8733559ad9ef5f614c95b3ccea1b743a))
* **MTMAI-2074:** Approval-SO confirm dialog handle unmet qty value ([01afee2](https://gitlab.com/maxxi-agro/atom/commit/01afee2a78f3298592b4b7c3db108bfcabcd52af))
* **MTMAI-2074:** Approval-SO TAG; change key calculate unmet product qty ([ef55fe9](https://gitlab.com/maxxi-agro/atom/commit/ef55fe9aa69b021515c90d43b32267b1239c6a5b))
* **MTMAI-2086:** Approval SPM form button wording -> "Setujui SPM" ([f242b61](https://gitlab.com/maxxi-agro/atom/commit/f242b61183375bf55117a44ca4111754a8278662))
* **MTMAI-2086:** Page title wording typo -> "Surat Perintah Muat" ([0534216](https://gitlab.com/maxxi-agro/atom/commit/05342164b5e952ac950b40b9768310890df47f5d))
* **MTMAI-2097:** approval-so-tag confirmation wording ([fad5c7c](https://gitlab.com/maxxi-agro/atom/commit/fad5c7c0668e330215428d52c9738de485f5b70d))
* **MTMAI-2102:** Approval-SO handle negative value of qty_stock ([f7d8f3b](https://gitlab.com/maxxi-agro/atom/commit/f7d8f3b41c95c64a6de41473fdbc965967c00d48))
* **MTMAI-2108:** Detai SO, validate approval privilege for konfirmasi finance ([3a34f7a](https://gitlab.com/maxxi-agro/atom/commit/3a34f7ae15ae4857683f0f0ff1e4b57e059f211d))
* **MTMAI-2119 MTMAI-2120:** detail rejected noteview; modal reject revert changes button link ([fd86319](https://gitlab.com/maxxi-agro/atom/commit/fd86319941dd4df72eda69f36e0edfca09ef0f48))
* **MTMAI-2162:** SPM Detail Page- handle view cta: save spm; approval tag ([b67fe7c](https://gitlab.com/maxxi-agro/atom/commit/b67fe7c77a06e9e8ec469051c47291b6d2addbe7))
* no fill reason when is locked in detail spm ([63e5678](https://gitlab.com/maxxi-agro/atom/commit/63e5678c8cbd4d242ca1c0ebeca9e17b0e949f6c))
* no filtered by status for table qty process sales order MTMAI-2164 ([72c19c2](https://gitlab.com/maxxi-agro/atom/commit/72c19c28b8b80e37fa1b35c189c6a5283b66cdb8))
* note view after approve spm MTMAI-2105 ([2e8f838](https://gitlab.com/maxxi-agro/atom/commit/2e8f838413e0e3bc8b694aa40c5d3c4f35cd4cf4))
* note view save spm error condition ([150a277](https://gitlab.com/maxxi-agro/atom/commit/150a2771433a8ec0d81e89028635dd036efde6b1))
* notification after reset one scan QR MTMAI-1995 ([b18fa06](https://gitlab.com/maxxi-agro/atom/commit/b18fa068d156f9e15edfd778e8218df9c74426b0))
* other product in spm detail sales order MTMAI-2101 ([61856f2](https://gitlab.com/maxxi-agro/atom/commit/61856f2ded5a45c3e1d2fc195fea8d644402464c))
* placeholder reject reason SO MTMAI-2118 ([68ea533](https://gitlab.com/maxxi-agro/atom/commit/68ea533adbfc02b9787b8acc71b48aafe5972eec))
* placeholder reject reason SO MTMAI-2118 ([6fb560f](https://gitlab.com/maxxi-agro/atom/commit/6fb560fbdc68af6566fd3efd30e67e5eb90952cf))
* plafon kredit render assurance value without type MTMAI-1539 ([c6b53a0](https://gitlab.com/maxxi-agro/atom/commit/c6b53a0a339af6780267fd196cfcece48048e160))
* **plafon kredit:** data plafon CBD should not have detail bills view MTMAI-1513 ([f02c4ec](https://gitlab.com/maxxi-agro/atom/commit/f02c4ecd9cb5e90c491ad79a42121fc279bb1472))
* price total create sales order MTMAI-2109 ([c2d02e2](https://gitlab.com/maxxi-agro/atom/commit/c2d02e2bcff34393ee17fb964bd585c373b4f9b6))
* privilege detail so MTMAI-2089 ([0715e37](https://gitlab.com/maxxi-agro/atom/commit/0715e37c868444a1314de54bf163eccdd13a8c53))
* product more spm list in detail so ([b7c787b](https://gitlab.com/maxxi-agro/atom/commit/b7c787bc4a15494aacfa8e8bae44c43dac127e58))
* product unscan tab MTMAI-2103 ([aa20594](https://gitlab.com/maxxi-agro/atom/commit/aa20594691f5c8788aba6bc3ff56d9fce356ab33))
* QR batch form; handle sync brand data MTMAI-1990 ([85d2bb8](https://gitlab.com/maxxi-agro/atom/commit/85d2bb815f21f2021d76b9b3ed72a07334416e04))
* **QR BATCH FORM:** Remove brand id from selected brand ([eb198e1](https://gitlab.com/maxxi-agro/atom/commit/eb198e1c33da1d754f1fb4aab9810acb9725fe2d))
* **qr-batch detail:** Note view render estimation date for on_progress qr batch MTMAI-1894 ([0a671ae](https://gitlab.com/maxxi-agro/atom/commit/0a671ae9946acb06b732bbfbebaf4655861a2529))
* **qr-batch detail:** tanggal dibuat format date MTMAI-1894 ([d3bf23f](https://gitlab.com/maxxi-agro/atom/commit/d3bf23f12886ef2b506240c86a727b8781906159))
* **qr-batch download:** add brand_id in qr-batch csv file MTMAI-2010 ([54adc7b](https://gitlab.com/maxxi-agro/atom/commit/54adc7bdbc67d1e20f631a88d8e0ef1796b69b5d))
* **qr-batch form:** Add confirmation dialog on cancel create batch MTMAI-1886 ([d4e7987](https://gitlab.com/maxxi-agro/atom/commit/d4e79870d7655b205ea96d840f8f82267ef8c4c2))
* **qr-batch form:** Add control name form error message MTMAI-1896 ([1e22f30](https://gitlab.com/maxxi-agro/atom/commit/1e22f3052d957b7fe9889742c70b642d21a4dccf))
* **qr-batch form:** Add form required validator ([2896649](https://gitlab.com/maxxi-agro/atom/commit/28966497472edf15fd52a016ff8062d4c1d94e20))
* **qr-batch form:** Input text render control error message MTMAI-1897 ([45ad99c](https://gitlab.com/maxxi-agro/atom/commit/45ad99cf45ea398ae72043240645b0c567873e06))
* reason reject finance MTMAI-2110 ([cd070d8](https://gitlab.com/maxxi-agro/atom/commit/cd070d8336ae83d6f4013699bc5d893a600d9bec))
* redirect after approve finance ([4602a72](https://gitlab.com/maxxi-agro/atom/commit/4602a727e5d910e5f07445cea7da21a9ff9c9de9))
* redirect detail breadcrumb spm ([0e2d3c5](https://gitlab.com/maxxi-agro/atom/commit/0e2d3c52dea229a066d9a6785160c96a1d843053))
* refresh data product in detail spm MTMAI-1977 ([cb8604a](https://gitlab.com/maxxi-agro/atom/commit/cb8604aeb8e11bf31d1c22cf25f2cc9b36b7b106))
* remove formatting date on document change data date; MTMAI-1377 ([080265c](https://gitlab.com/maxxi-agro/atom/commit/080265c5b2f690dad410127e383b6ad8ea8fdec5))
* remove temp. role privilege Qr Produk ([a0243ab](https://gitlab.com/maxxi-agro/atom/commit/a0243abce8fdc174b0c71b5d0db65ee51a5feebd))
* reset filter SO, PO & SPM MTMAI-2051 ([e3f4222](https://gitlab.com/maxxi-agro/atom/commit/e3f42221bffab65c709cd53cfd95788a3d37f374))
* revamp detail SO MTMAI-1526, MTMAI-1527 ([4bc1128](https://gitlab.com/maxxi-agro/atom/commit/4bc112820324728771820c2c7aa4e435a73726ac))
* revamp finance check in detail SO MTMAI-1525 ([ec3c4a2](https://gitlab.com/maxxi-agro/atom/commit/ec3c4a2f0d5520afee73cf781041ec2f4b0c9669))
* revamp PO and fix bug MTMAI-2062, MTMAI-2059, MTMAI-2041 ([c94cbde](https://gitlab.com/maxxi-agro/atom/commit/c94cbdef66b6c90eefc1981f969d59d7b8cb63cd))
* show created date on tab SO in detail PO & fix design create SO MTMAI-2021, MTMAI-2020, MTMAI-2019 ([c8ce0c2](https://gitlab.com/maxxi-agro/atom/commit/c8ce0c22688c895f796e8dc695938fbe3dbf94a2))
* show sales order in detail PO ([e711acc](https://gitlab.com/maxxi-agro/atom/commit/e711acc3c91fd419f930186d99bf2df72e871c84))
* **SO Approval-TAG:** change payload key qty of sales_order_items_request MTMAI-2016 ([48d8aa5](https://gitlab.com/maxxi-agro/atom/commit/48d8aa59b70338c802ac8940b3096fcbf1bc6654))
* sorting PO MTMAI-1907, MTMAI-1906 ([9ef51ef](https://gitlab.com/maxxi-agro/atom/commit/9ef51ef6457a88805bebd4b5671af6176b36cd37))
* tab product unscan in detail so ([b8882bd](https://gitlab.com/maxxi-agro/atom/commit/b8882bd651c459b780b1aefaa7ba289a6b3b9a1a))
* table column and placeholder filter PO MTMAI-1913, MTMAI-1912, MTMAI-1911, MTMAI-1910, MTMAI-1908 ([dd0b2de](https://gitlab.com/maxxi-agro/atom/commit/dd0b2de08c2071a8567c801b14e90077786f5eeb))
* **table-data:** Add sorter column MTMAI-1989 ([d168cda](https://gitlab.com/maxxi-agro/atom/commit/d168cdacc33b8dfecce05e90a6d65f02f3bda7a7))
* **table-data:** QR product list add table sorter MTMAI-1989 ([60cd739](https://gitlab.com/maxxi-agro/atom/commit/60cd739bd277382320428afb953a4fa399207104))
* **table-pagination:** QR Batch product list; handle pagination MTMAI-1993 ([36c8075](https://gitlab.com/maxxi-agro/atom/commit/36c807576bcf4e9b611bda33ea79f9cc7a1a839b))
* total product list SO ([90d2c71](https://gitlab.com/maxxi-agro/atom/commit/90d2c71bf925ff48c2d47393b80d86bbb883373b))
* try again save SPM MTMAI-1975 ([29edcb8](https://gitlab.com/maxxi-agro/atom/commit/29edcb8472c5490f30a41736300505bf06e9b171))
* typo btn name ([24a187f](https://gitlab.com/maxxi-agro/atom/commit/24a187f6b0bda74d7f93db94b8a6d2a3fe6d3c93))
* validate btn create SO and modal product not full fill MTMAI-2011, MTMAI-2012 ([cc2abb4](https://gitlab.com/maxxi-agro/atom/commit/cc2abb4e48565382c96f84bbaac8e2ba94c134bc))
* validate create so if warehouse center MTMAI-1978 ([6f81318](https://gitlab.com/maxxi-agro/atom/commit/6f813181bd46c8a89b7fd5e236262bcfd77b7d27))
* validate max filter date SO and SPM MTMAI-2049 ([429702d](https://gitlab.com/maxxi-agro/atom/commit/429702d713f8d5d00ed7eae1add6cd361e8807c6))
* warning price summary ([b7b775e](https://gitlab.com/maxxi-agro/atom/commit/b7b775e8d70e3f7b67a57c6691e10e58f9819652))
* wording and no validate btn unscan MTMAI-2002 ([3700d92](https://gitlab.com/maxxi-agro/atom/commit/3700d922bbbdc04ec791d41cb270e6db516b360f))
* wording empty PO MTMAI-1917 ([3b0d309](https://gitlab.com/maxxi-agro/atom/commit/3b0d30922470546de4d609a3cffecaa2c05878e9))
* wording note view modal approve MTMAI-2168 ([1f6ed00](https://gitlab.com/maxxi-agro/atom/commit/1f6ed00a9dc1de2d4d35993e315197833ee0a81b))
* wording note view modal approve MTMAI-2168 ([54feb52](https://gitlab.com/maxxi-agro/atom/commit/54feb52729d84f1a279cf618305bd4425d40cc69))

## Release V1.4.2
- Hotfix Area Select Edit Data

## Release V1.4.1
- Hot Fix Button Create Empty State
## Release V1.4.0

- Added Discount Settings Menu
- Added Discount Setting Form
- Added CDB Discount list & history
- Added Sales Discount list & history
- Added Image preview for dokumen CBD
- Fix Create SO from detail PO
- Fix PO list filter with status
- Fix remove approval button on expired SO 

## Release V1.3.0

- Added Report Menu
- Added Sales Number in detail Retailer
- Fix wording change Area Manager to Regional Head
- Fix User Form
- Fix Edit Product Form
- Fix Distributor Info Owner name
- Remove unused import package

## Release V1.2.3

- Fix Bug Sub Area
- Fix Privilage In action detail product

## Release V1.2.2

- Clear Local Storage
- So Penanagihan tab
- Show Discount

## Release V1.2.1

- Image Variant Fix
- Handle Slide Nex
- validate qr

## Release V1.2.0

- RBA Setup
- Setup Privilege
- Show and hide menu
- Revamp Product Catalog Menu
- And fix bug

## Release V1.0.0

- Init Project With MVP data