<ng-container>
  <!--    <pre>discount term payload: {{ DiscountTermPayload | json }}</pre>-->
  <app-card cardClasses="mx-auto w-80">
    <div cardBody class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1 px-8 py-8 w-100">
        <!--:: title -->
        <h2 class="mb-10">{{ FormTitle }}</h2>
        <!--:: sections form -->
        <ng-container [ngSwitch]="params.type">
          <ng-container *ngSwitchCase="EnumProgramMarketingType.ONE_SHOOT" [ngTemplateOutlet]="oneShootFormTpl"></ng-container>
          <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT" [ngTemplateOutlet]="discountProductFormTpl"></ng-container>
          <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE" [ngTemplateOutlet]="discountPurchaseFormTpl"></ng-container>
          <ng-container *ngSwitchCase="EnumProgramMarketingType.PRODUCT_COMPENSATION" [ngTemplateOutlet]="compensationFormTpl"></ng-container>
        </ng-container>
      </div>
    </div>

    <ng-template #oneShootFormTpl>
      <ng-container [ngTemplateOutlet]="informasiProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="ketentuanProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="ketentuanPurchaseOrderTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="settingPurchaseRewardTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="ketentuanDiskonTpl"></ng-container>

      <!--      <ng-container [ngTemplateOutlet]="ketentuanHadiahTpl"></ng-container>-->
    </ng-template>

    <ng-template #discountProductFormTpl>
      <ng-container [ngTemplateOutlet]="informasiProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="ketentuanProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="ketentuanDiskonTpl"></ng-container>
    </ng-template>

    <ng-template #discountPurchaseFormTpl>
      <ng-container [ngTemplateOutlet]="informasiProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="ketentuanProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <app-card-form-order-and-discount-term />
      <!--      <ng-container [ngTemplateOutlet]="ketentuanPembelianTpl"></ng-container>-->
      <!--      <hr class="my-8 border-gray-300" />-->
      <!--      <ng-container [ngTemplateOutlet]="ketentuanDiskonTpl"></ng-container>-->
    </ng-template>

    <ng-template #compensationFormTpl>
      <ng-container [ngTemplateOutlet]="informasiProgramTpl"></ng-container>
      <hr class="my-8 border-gray-300" />

      <ng-container [ngTemplateOutlet]="informasiKompensasiTpl"></ng-container>
    </ng-template>
  </app-card>

  <div class="w-80 mx-auto d-flex justify-content-between my-5">
    <button (click)="handleCancel()" class="btn btn-outline btn-outline-secondary btn-cancel min-w-150px" type="button">
      <span class="text-primary fw-bold">Batal</span>
    </button>
    <!--    validateForm()-->
    <button (click)="handleSubmit()" [disabled]="validateForm()" class="btn btn-primary min-w-150px" type="submit">Simpan</button>
  </div>

  <ng-template #informasiProgramTpl>
    <!--    <pre>programInfoRef form.valid: {{ programInfoRef.form.valid }}</pre>-->
    <app-program-information-form #programInfoRef [data]="detailProgram && detailProgram.information" [usePeriod]="getUsePeriod()" />
  </ng-template>

  <ng-template #ketentuanProgramTpl>
    <!--    <pre>programTermRef  form.valid: {{ programTermRef.form.valid }}</pre>-->
    <app-program-term-form #programTermRef [data]="detailProgram && detailProgram.program_term" />
  </ng-template>

  <ng-template #ketentuanPembelianTpl>
    <!--    <pre>orderTermRef form.valid: {{ orderTermRef.form.valid }}</pre>-->
    <app-order-term-form #orderTermRef [data]="detailProgram && detailProgram.order_term" />
  </ng-template>

  <ng-template #ketentuanPurchaseOrderTpl>
    <!--        <pre>orderTermRef form.valid: {{ purchaseOrderTermRef.form.valid }}</pre>-->
    <app-purchase-order-term-form #purchaseOrderTermRef [data]="detailProgram && detailProgram.purchase_order_term" />
  </ng-template>

  <ng-template #ketentuanDiskonTpl>
    <!--    <pre>DiscountTermForm.valid: {{ DiscountTermForm?.valid }}</pre>-->
    <app-discount-term-form #discountTermRef [data]="detailProgram && detailProgram.discount_term" [type]="params.type" />
  </ng-template>

  <ng-template #settingPurchaseRewardTpl>
    <app-setting-reward-purchase #settingPurchaseRewardRef />
  </ng-template>

  <ng-template #ketentuanHadiahTpl>
    <!--    <pre>RewardTermForm.valid {{ RewardTermForm?.valid }}</pre>-->
    <app-reward-term-form #rewardTermRef [data]="detailProgram && detailProgram.reward" />
  </ng-template>

  <ng-template #informasiKompensasiTpl>
    <!--    <pre>CompensationTermForm.valid: {{ CompensationTermForm?.valid }}</pre>-->
    <app-compensation-term-form #compensationTermRef [data]="detailProgram && detailProgram.compensation" />
  </ng-template>
</ng-container>

<app-modal #modalResponse [modalConfig]="modalConfigResponse">
  <div class="d-flex flex-column justify-content-center align-items-center">
    <ng-container *ngIf="isLoadingSubject | async; else responseTpl">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>

    <ng-template #responseTpl>
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="mb-n8">
        {{ messageResponse | async }}
      </div>
    </ng-template>
  </div>
</app-modal>

<app-modal-confirmation-program-marketing
  #modalConfirmation
  [programInfoRef]="programInfoRef"
  [programTermRef]="programTermRef"
  [orderTermRef]="orderTermRef"
  [discountTermRef]="discountTermRef"
  [rewardTermRef]="rewardTermRef"
  [compensationTermRef]="compensationTermRef"
  (callBackSubmit)="onSubmitForm()"
>
</app-modal-confirmation-program-marketing>
