{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./out-tsc/spec", "types": ["jest", "node"], "esModuleInterop": true, "allowSyntheticDefaultImports": true, "paths": {"@utils/*": ["src/app/core/utils/*"], "@directives/*": ["src/app/directives/*"], "@environments/*": ["src/environments/*"], "@guards/*": ["src/app/guards/*"], "@models/*": ["src/app/models/*"], "@pages/*": ["src/app/pages/*"], "@services/*": ["src/app/services/*"], "@shared/*": ["src/app/shared/*"], "@config/*": ["src/app/config/*"], "@metronic/*": ["src/app/_metronic/*"]}}, "files": ["src/setup-jest.ts"], "include": ["src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.d.ts", "src/global.d.ts"], "exclude": ["src/test.ts"]}