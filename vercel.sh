#!/bin/bash

# Enhanced Vercel build script with validation and monitoring
# Fallback for empty VERCEL_GIT_BRANCH
BRANCH=${VERCEL_GIT_BRANCH:-$VERCEL_GIT_COMMIT_REF}

echo "🚀 Vercel Build Starting..."
echo "================================"
echo "VERCEL_ENV: $VERCEL_ENV"
echo "BRANCH: $BRANCH"
echo "COMMIT: $VERCEL_GIT_COMMIT_SHA"
echo "TIMESTAMP: $(date)"
echo "================================"

# Validation checks
if [[ -z "$BRANCH" ]]; then
  echo "❌ ERROR: Branch information not available"
  exit 1
fi

# Environment validation
case "$VERCEL_ENV" in
  "production")
    if [[ "$BRANCH" != "main" ]]; then
      echo "⚠️  WARNING: Production build triggered from non-main branch: $BRANCH"
    fi
    ;;
  "preview")
    echo "✅ Preview deployment for branch: $BRANCH"
    ;;
  *)
    echo "⚠️  Unknown Vercel environment: $VERCEL_ENV"
    ;;
esac

# Build process with enhanced logging
echo ""
echo "🏗️  Starting build process..."

if [[ $VERCEL_ENV == "production" ]]; then
  echo "🎯 Building for PRODUCTION environment..."
  echo "📋 Using production configuration"
  npm run build
  BUILD_STATUS=$?
elif [[ $VERCEL_ENV == "preview" && $BRANCH == "staging" ]]; then
  echo "🎯 Building for STAGING environment..."
  echo "📋 Using staging configuration"
  npm run build-staging
  BUILD_STATUS=$?
elif [[ $VERCEL_ENV == "preview" && $BRANCH == "development-second" ]]; then
  echo "🎯 Building for DEVELOPMENT-SECOND environment..."
  echo "📋 Using development-second configuration"
  npm run build-dev2
  BUILD_STATUS=$?
else
  echo "🎯 Building for DEVELOPMENT environment..."
  echo "📋 Using development configuration (default)"
  npm run build-dev
  BUILD_STATUS=$?
fi

# Build result validation
echo ""
if [[ $BUILD_STATUS -eq 0 ]]; then
  echo "✅ Build completed successfully!"
  echo "📊 Build artifacts ready for deployment"
else
  echo "❌ Build failed with exit code: $BUILD_STATUS"
  echo "🔍 Check build logs above for details"
  exit $BUILD_STATUS
fi

echo "🎉 Vercel build process completed"
echo "🚀 Deployment will proceed automatically"

