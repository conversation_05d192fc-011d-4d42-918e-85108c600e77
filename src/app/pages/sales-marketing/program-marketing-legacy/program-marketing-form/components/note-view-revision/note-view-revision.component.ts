import { Component, Input } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-note-view-revision',
  templateUrl: './note-view-revision.component.html',
  styleUrls: ['./note-view-revision.component.scss'],
})
export class NoteViewRevisionComponent {
  @Input() title: string = 'Catatan Perbaikan';
  @Input() note: string = 'Seharusnya Sales Diskon Mengikuti Sales Discount, bukan tanpa Sales Discount';

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
