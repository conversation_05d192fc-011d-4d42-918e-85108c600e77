import * as fs from 'fs';
import * as path from 'path';

interface ChangelogEntry {
  version: string;
  date: Date;
  dateString: string;
  content: string;
}

interface ArchiverOptions {
  mainChangelogPath?: string;
  archiveDir?: string;
  keepMonths?: number;
  header?: string;
}

interface ArchivedFile {
  year: number;
  filename: string;
  count: number;
}

interface FilterResult {
  recent: ChangelogEntry[];
  archive: ChangelogEntry[];
}

class ChangelogArchiver {
  private mainChangelogPath: string;
  private archiveDir: string;
  private keepMonths: number;
  private header: string;

  constructor(options: ArchiverOptions = {}) {
    this.mainChangelogPath = options.mainChangelogPath || 'CHANGELOG.md';
    this.archiveDir = options.archiveDir || './docs';
    this.keepMonths = options.keepMonths || 6;
    this.header = options.header || '# Backoffice Changelog';
  }

  /**
   * Parse changelog content dan extract entries berdasarkan version dan tanggal
   */
  private parseChangelog(content: string): ChangelogEntry[] {
    const lines = content.split('\n');
    const entries: ChangelogEntry[] = [];
    let currentEntry: Partial<ChangelogEntry> | null = null;
    let currentContent: string[] = [];

    console.log(`🔍 Parsing ${lines.length} lines...`);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Debug: print first few lines to see format
      // if (i < 10) {
      //   console.log(`Line ${i}: "${line}"`);
      // }

      // Detect version header - support multiple formats
      // Format 1: ## [1.13.3](url) (2025-06-05)
      // Format 2: **1.13.3 (2025-06-05)**
      let versionMatch = line.match(/^##\s+\[(\d+\.\d+\.\d+)\]\([^)]+\)\s+\((\d{4}-\d{2}-\d{2})\)/);
      if (!versionMatch) {
        versionMatch = line.match(/^\*\*(\d+\.\d+\.\d+)\s+\((\d{4}-\d{2}-\d{2})\)\*\*/);
      }

      if (versionMatch) {
        console.log(`✅ Found version: ${versionMatch[1]} (${versionMatch[2]})`);

        // Save previous entry
        if (currentEntry && currentEntry.version && currentEntry.date) {
          currentEntry.content = currentContent.join('\n');
          entries.push(currentEntry as ChangelogEntry);
          console.log(`💾 Saved entry for version ${currentEntry.version}`);
        }

        // Start new entry
        currentEntry = {
          version: versionMatch[1],
          date: new Date(versionMatch[2]),
          dateString: versionMatch[2],
          content: '',
        };
        currentContent = [line];
      } else if (currentEntry) {
        currentContent.push(line);
      } else if (line.trim() && !line.startsWith('#')) {
        // Content sebelum version pertama (biasanya header)
        currentContent.push(line);
      }
    }

    // Add last entry
    if (currentEntry && currentEntry.version && currentEntry.date) {
      currentEntry.content = currentContent.join('\n');
      entries.push(currentEntry as ChangelogEntry);
      console.log(`💾 Saved last entry for version ${currentEntry.version}`);
    }

    console.log(`📈 Total entries parsed: ${entries.length}`);
    return entries;
  }

  /**
   * Filter entries berdasarkan umur
   */
  private filterEntriesByAge(entries: ChangelogEntry[]): FilterResult {
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - this.keepMonths);

    const recent = entries.filter((entry) => entry.date >= cutoffDate);
    const archive = entries.filter((entry) => entry.date < cutoffDate);

    return { recent, archive };
  }

  /**
   * Group archived entries by year
   */
  private groupByYear(entries: ChangelogEntry[]): Record<number, ChangelogEntry[]> {
    const groups: Record<number, ChangelogEntry[]> = {};

    entries.forEach((entry) => {
      const year = entry.date.getFullYear();
      if (!groups[year]) {
        groups[year] = [];
      }
      groups[year].push(entry);
    });

    return groups;
  }

  /**
   * Generate changelog content from entries
   */
  private generateChangelogContent(entries: ChangelogEntry[], header: string = this.header): string {
    let content = `${header}\n\n`;

    entries.forEach((entry) => {
      content += entry.content + '\n';
    });

    return content.trim();
  }

  /**
   * Create atau update archived changelog files
   */
  private async createArchivedFiles(archivedGroups: Record<number, ChangelogEntry[]>): Promise<ArchivedFile[]> {
    const promises = Object.keys(archivedGroups).map(async (yearStr): Promise<ArchivedFile> => {
      const year = parseInt(yearStr);
      const filename = `CHANGELOG-${year}.md`;
      const filepath = path.join(this.archiveDir, filename);
      const entries = archivedGroups[year];

      // Sort entries by date (newest first)
      entries.sort((a, b) => b.date.getTime() - a.date.getTime());

      const content = this.generateChangelogContent(entries, `# Backoffice Changelog - ${year}`);

      await fs.promises.writeFile(filepath, content, 'utf8');
      console.log(`📁 Archived ${entries.length} entries to ${filename}`);

      return { year, filename, count: entries.length };
    });

    return Promise.all(promises);
  }

  /**
   * Update main changelog dengan recent entries saja
   */
  private async updateMainChangelog(recentEntries: ChangelogEntry[]): Promise<void> {
    // Sort recent entries by date (newest first)
    recentEntries.sort((a, b) => b.date.getTime() - a.date.getTime());

    const content = this.generateChangelogContent(recentEntries);
    await fs.promises.writeFile(this.mainChangelogPath, content, 'utf8');

    console.log(`📝 Updated main changelog with ${recentEntries.length} recent entries`);
  }

  /**
   * Main archive function
   */
  public async archive(): Promise<void> {
    try {
      console.log('🚀 Starting changelog archiving process...');

      // Read current changelog
      if (!fs.existsSync(this.mainChangelogPath)) {
        console.log('❌ CHANGELOG.md not found!');
        return;
      }

      const content = await fs.promises.readFile(this.mainChangelogPath, 'utf8');

      // Parse entries
      const entries = this.parseChangelog(content);
      console.log(`📊 Found ${entries.length} total entries`);

      if (entries.length === 0) {
        console.log('ℹ️  No entries to process');
        return;
      }

      // Filter by age
      const { recent, archive } = this.filterEntriesByAge(entries);
      console.log(`📅 Recent: ${recent.length}, Archive: ${archive.length}`);

      if (archive.length === 0) {
        console.log('✅ No entries need archiving');
        return;
      }

      // Group archived entries by year
      const archivedGroups = this.groupByYear(archive);

      // Create archived files
      const archivedFiles = await this.createArchivedFiles(archivedGroups);

      // Update main changelog
      await this.updateMainChangelog(recent);

      // Summary
      console.log('\n✅ Archive process completed!');
      console.log('📁 Created/Updated files:');
      archivedFiles.forEach((file) => {
        console.log(`   - ${file.filename} (${file.count} entries)`);
      });
      console.log(`📝 Main changelog: ${recent.length} recent entries kept`);
    } catch (error) {
      console.error('❌ Archive process failed:', (error as Error).message);
      process.exit(1);
    }
  }
}

// CLI execution
if (require.main === module) {
  const archiver = new ChangelogArchiver({
    mainChangelogPath: 'CHANGELOG.md',
    archiveDir: './docs',
    keepMonths: 6, // Keep 6 months of recent entries
    header: '# Backoffice Changelog',
  });

  archiver.archive();
}

export default ChangelogArchiver;
