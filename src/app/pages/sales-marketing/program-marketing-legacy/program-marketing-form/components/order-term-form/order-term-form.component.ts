import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { IAccumulation, IBundlingVariant, IOrderTerm } from '../../../program-marketing-legacy.interface';
import { InputSelectMaterialComponent } from '@shared/components/form/input-select-material/input-select-material.component';
import { IGenericValueDisplay, IGenericValueDisplayDescription, IGenericValueDisplayDisable } from '@shared/interface/generic';
import { auditTime, BehaviorSubject, distinctUntilChanged } from 'rxjs';
import { InputSelectMaterialInterface } from '@shared/components/form/input-select-material/input-select-material.interface';
import { API } from '@config/constants/api.constant';
import { IChips } from '@shared/components/v1/chips/chips.interface';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { UtilitiesService } from '@services/utilities.service';
import { EnumProgramMarketingAccumulationType, EnumProgramMarketingPOType, EnumProgramMarketingRewardType, EnumProgramMarketingStatus } from '../../../program-marketing.enum';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { DistributorProgramFormService } from '../../../../program-marketing/program-marketing-distributor/distributor-program-form/distributor-program-form.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-order-term-form',
  templateUrl: './order-term-form.component.html',
  styleUrls: ['./order-term-form.component.scss'],
})
export class OrderTermFormComponent implements OnInit, OnChanges {
  @Input() data!: IOrderTerm;
  @Output() orderProduct = new EventEmitter<FormGroup>();
  @ViewChild('inputSelectVariant') inputSelectVariant!: InputSelectMaterialComponent;

  optionRadioPurchase: IGenericValueDisplayDescription[] = [];
  optionRadioReward: IGenericValueDisplay[] = [];
  optionRadioMinimalPurchase: IGenericValueDisplayDisable[] = [];

  listProductSubject = new BehaviorSubject<InputSelectMaterialInterface[]>([]);
  isLoadingSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isDisableForm = true;

  ENDPOINT_PRODUCT_GROUP: string = API.PROGRAM_MARKETING.GET_PRODUCT_GROUP;
  valueProduct: IChips[] = [];

  shouldResetListBox = false;

  productVariant: any[] = [];
  form: FormGroup = this.fb.group({
    purchase_order_type: new FormControl('', [Validators.required]),
    bundling: this.fb.group({
      variants: new FormArray<any | []>([]),
    }),
    accumulation: this.fb.group({
      accumulation_type: new FormControl('', []),
      variant_ids: new FormControl<any[]>([], []),
      minimum_qty: new FormControl('', []),
      minimum_price: new FormControl('', []),
      minimum_weight_or_volume: new FormControl('', []),
    }),
    product: new FormControl([], [Validators.required]),
  });

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data) {
      this.data = changes.data.currentValue;
      this.handleData();
      this.handleFormChanges();
    }
  }

  get GetPayload() {
    const bundling = {
      variants: this.variantForm.value.map((value: IBundlingVariant) => {
        return {
          variant_id: value.variant_id,
          qty: parseInt(value.qty?.toString()?.replace('.', '') ?? ''),
        };
      }) as IBundlingVariant[],
    };
    const accumulation = {
      ...this.accumulationForm.value,
      variant_ids: this.productForm.value.map((value: any) => value.id),
      minimum_qty: this.utils.stringNumberToPayload(this.qtyAccumulationForm.value?.toString()),
      minimum_price: this.utils.stringNumberToPayload(this.priceAccumulationForm.value?.toString()),
      minimum_weight_or_volume: this.utils.stringNumberToPayload(this.volumeAccumulationForm.value?.toString()),
    };
    const orderTerm: IOrderTerm = {
      ...this.form.value,
      purchase_order_type: this.purchaseOrderTypeForm.value as EnumProgramMarketingPOType.BUNDLING | EnumProgramMarketingPOType.ACCUMULATION,
      bundling: bundling,
      accumulation: accumulation,
    };
    return orderTerm;
  }

  get FormValue() {
    return this.form.value;
  }

  get purchaseOrderTypeForm() {
    return this.form.get('purchase_order_type') as FormControl;
  }

  get productForm() {
    return this.form.get('product') as FormControl;
  }

  get bundlingForm() {
    return this.form.get('bundling') as FormGroup;
  }

  get variantForm() {
    return this.bundlingForm.get('variants') as FormArray;
  }

  get accumulationForm() {
    return this.form.get('accumulation') as FormGroup;
  }

  get accumulationTypeEnumForm() {
    return this.accumulationForm.get('accumulation_type') as FormControl;
  }

  get qtyAccumulationForm() {
    return this.accumulationForm.get('minimum_qty') as FormControl;
  }

  get priceAccumulationForm() {
    return this.accumulationForm.get('minimum_price') as FormControl;
  }

  get volumeAccumulationForm() {
    return this.accumulationForm.get('minimum_weight_or_volume') as FormControl;
  }

  constructor(
    private fb: FormBuilder,
    private utils: UtilitiesService,
    private formService: DistributorProgramFormService,
    private ref: ChangeDetectorRef,
    private activeRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.isLoadingSubject.next(true);
    // this.getRewardProduct();
    this.getOptionRadio();
    this.changeForm();
    this.isLoadingSubject.next(false);
  }

  getOptionRadio() {
    this.optionRadioPurchase = [
      {
        value: EnumProgramMarketingPOType.BUNDLING,
        display: 'Bundling',
        description: 'Semua produk yang di tambah harus dibeli dalam 1 PO.',
      },
      {
        value: EnumProgramMarketingPOType.ACCUMULATION,
        display: 'Akumulasi/Gabungan Produk',
        description: 'Tidak harus membeli semua produk dalam 1 PO.',
      },
    ];

    this.optionRadioReward = [
      {
        value: EnumProgramMarketingRewardType.MAI_PRODUCT,
        display: 'Produk Maxxi Agri',
      },
      {
        value: EnumProgramMarketingRewardType.NON_MAI_PRODUCT,
        display: 'Lainnya',
      },
    ];

    this.optionRadioMinimalPurchase = [
      {
        value: 'minimum_weight_or_volume',
        display: 'Berat/Volume',
        disable: false,
      },
      {
        value: 'minimum_qty',
        display: 'Jumlah(QTY)',
        disable: false,
      },
      {
        value: 'minimum_price',
        display: 'Nominal(Rupiah)',
        disable: false,
      },
    ];
  }

  // validatorRewardType(value: string) {
  //   if (value === EnumProgramMarketingRewardType.NON_MAI_PRODUCT) {
  //     this.form.get('variant_reward')?.clearValidators();
  //     this.form.get('qty_reward')?.clearValidators();
  //
  //     this.form.get('product_other_reward')?.setValidators([Validators.required]);
  //     this.form.get('maximum_budget_other_reward')?.setValidators([Validators.required]);
  //   } else {
  //     this.form.get('product_other_reward')?.clearValidators();
  //     this.form.get('maximum_budget_other_reward')?.clearValidators();
  //
  //     this.form.get('variant_reward')?.setValidators([Validators.required]);
  //     this.form.get('qty_reward')?.setValidators([Validators.required]);
  //   }
  // }

  clearValidatorAccumulation() {
    this.volumeAccumulationForm.clearValidators();
    this.qtyAccumulationForm.clearValidators();
    this.priceAccumulationForm.clearValidators();
    this.volumeAccumulationForm.updateValueAndValidity();
    this.qtyAccumulationForm.updateValueAndValidity();
    this.priceAccumulationForm.updateValueAndValidity();
  }

  validatorPOType(value: string) {
    if (value === EnumProgramMarketingPOType.ACCUMULATION) {
      let _type = null;
      if (this.data) {
        const weight = this.data?.accumulation?.minimum_weight_or_volume && 'minimum_weight_or_volume';
        const qty = this.data?.accumulation?.minimum_qty && 'minimum_qty';
        const price = this.data?.accumulation?.minimum_price && 'minimum_price';
        _type = (weight || qty || price)?.toString() ?? '';
      }
      this.validatorMinimumPOForm(_type);
    } else {
      this.clearValidatorAccumulation();
    }
  }

  validatorMinimumPOForm(value: string | null) {
    if (!value) return;

    const minimumQty = this.qtyAccumulationForm;
    const minimumPrice = this.priceAccumulationForm;
    const minimumWeight = this.volumeAccumulationForm;

    if (value === 'minimum_weight_or_volume') {
      minimumQty.clearValidators();
      minimumQty.reset();
      minimumPrice.clearValidators();
      minimumPrice.reset();
    } else if (value === 'minimum_qty') {
      minimumWeight.clearValidators();
      minimumWeight.reset();
      minimumPrice.clearValidators();
      minimumPrice.reset();
    } else {
      minimumWeight.clearValidators();
      minimumWeight.reset();
      minimumQty.clearValidators();
      minimumQty.reset();
    }
    minimumQty.updateValueAndValidity();
    minimumPrice.updateValueAndValidity();
    minimumWeight.updateValueAndValidity();
  }

  clearValue(type: string) {
    this.productForm.reset();
    this.variantForm.clear();
    this.valueProduct = [];

    if (type === EnumProgramMarketingPOType.ACCUMULATION) {
      this.bundlingForm.reset();
    } else {
      this.accumulationForm.reset();
      this.clearValidatorAccumulation();
    }

    this.ref.detectChanges();
  }

  changeForm() {
    this.purchaseOrderTypeForm.valueChanges.subscribe((value) => {
      if (!value) return;
      this.validatorPOType(value);
    });

    this.productForm.valueChanges.subscribe((products) => {
      if (this.purchaseOrderTypeForm.value !== EnumProgramMarketingPOType.ACCUMULATION) return;

      // Reset all options if products list is empty or invalid
      if (!Array.isArray(products) || products.length === 0) {
        this.optionRadioMinimalPurchase.forEach((item) => (item.disable = false));
        return;
      }

      const uniqueSaleUnits = new Set<string>();
      const uniqueDeliveryUnits = new Set<string>();

      products.forEach((variant: any) => {
        if (variant?.sale_unit) uniqueSaleUnits.add(variant.sale_unit);
        if (variant?.delivery_unit) uniqueDeliveryUnits.add(variant.delivery_unit);
      });

      this.optionRadioMinimalPurchase[0].disable = uniqueDeliveryUnits.size > 1; // berat/volume
      this.optionRadioMinimalPurchase[1].disable = uniqueSaleUnits.size > 1; // jumlah/qty
    });

    this.accumulationTypeEnumForm.valueChanges.subscribe((value) => {
      if (!value) return;
      this.validatorMinimumPOForm(value);
    });
  }

  productSelection(chips: BehaviorSubject<any>): void {
    if (!chips?.value) return;

    this.productVariant = chips.value
      .filter(() => this.purchaseOrderTypeForm.value === EnumProgramMarketingPOType.BUNDLING)
      .flatMap((value: any) => value.variants.filter((variant: any) => variant.selected));

    this.variantForm.clear();
    this.productVariant.forEach((variant) => this.variantForm.push(this.addVariantControl(variant)));
  }

  addVariantControl(v: any) {
    const weight = v.qty && v.delivery_ratio ? v.qty * v.delivery_ratio : 0;
    return this.fb.group({
      ...v,
      variant_id: this.fb.control(v.id ? v.id : '', [Validators.required]),
      qty: this.fb.control(v.qty ? v.qty : '', [Validators.required]),
      weight: this.fb.control(weight, [Validators.required]),
    });
  }

  changeQtyVariant(e: any, i: number, deliveryRatio: number) {
    return this.variantForm
      .at(i)
      .get('weight')
      ?.setValue((e.target.value !== '' ? parseInt(e.target.value) : 0) * deliveryRatio);
  }

  handleData() {
    if (this.data) {
      const status = this.activeRoute.snapshot.queryParams['status'];
      this.isDisableForm = status ? status === EnumProgramMarketingStatus.ACTIVE : !!this.data.revision_note;
      this.validatorPOType(this.data.purchase_order_type);
      let _type = '';
      if (this.data.purchase_order_type === EnumProgramMarketingPOType.ACCUMULATION && this.data.accumulation) {
        const { minimum_weight_or_volume, minimum_qty, minimum_price } = this.data.accumulation;
        this.clearValidatorAccumulation();

        if (this.data.accumulation.minimum_weight_or_volume > 0) {
          _type = this.optionRadioMinimalPurchase[0].value as string;
          this.volumeAccumulationForm.patchValue(minimum_weight_or_volume);
        } else if (this.data.accumulation.minimum_qty > 0) {
          _type = this.optionRadioMinimalPurchase[1].value as string;
          this.qtyAccumulationForm.patchValue(minimum_qty);
        } else {
          _type = this.optionRadioMinimalPurchase[2].value as string;
          this.priceAccumulationForm.patchValue(minimum_price);
        }
        this.accumulationTypeEnumForm.patchValue(_type);
      } else {
      }
      //
      if (this.data.product_variant && this.data.product_variant.length > 0) {
        this.valueProduct = this.data.product_variant;
        this.productForm?.patchValue(this.data.product_variant);
        if (this.data.purchase_order_type === EnumProgramMarketingPOType.BUNDLING) {
          this.data.bundling &&
            this.data.bundling.variants?.forEach((v) => {
              this.variantForm.push(this.addVariantControl(v));
            });
        }
      }

      const bundling = {
        ...this.data.bundling,
        variants: this.data.bundling?.variants?.map((value: IBundlingVariant) => {
          return {
            variant_id: value.variant_id,
            qty: this.utils.stringNumberToPayload(value.qty?.toString() ?? ''),
          };
        }) as IBundlingVariant[],
      };
      const accumulation: IAccumulation = {
        ...this.data.accumulation,
        variant_ids: [],
        minimum_weight_or_volume: this.data.accumulation?.minimum_weight_or_volume ?? 0,
        minimum_qty: this.data.accumulation?.minimum_qty ?? 0,
        minimum_price: this.data.accumulation?.minimum_price ?? 0,
      };
      const data: IOrderTerm = {
        ...this.data,
        bundling,
        accumulation,
      };
      this.form.patchValue(data);
    }
  }

  typePOClick(option: IGenericValueDisplayDescription) {
    this.clearValue(option.value.toString());
    this.shouldResetListBox = true;
    setTimeout(() => (this.shouldResetListBox = false), 0);
  }

  handleFormChanges() {
    this.form.valueChanges
      .pipe(
        auditTime(0),
        distinctUntilChanged((previous, current) => JSON.stringify(previous) === JSON.stringify(current))
      )
      .subscribe(() => this.formService.updateOrderTermForm(this.form));
  }

  displayMinimumWeight() {
    return this.productForm.value?.[0].delivery_unit;
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumProgramMarketingPOType = EnumProgramMarketingPOType;
  protected readonly EnumProgramMarketingAccumulationType = EnumProgramMarketingAccumulationType;
  protected readonly EnumProgramMarketingRewardType = EnumProgramMarketingRewardType;
}
