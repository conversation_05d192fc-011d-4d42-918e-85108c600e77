{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"atom": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/atom"}, "allowedCommonJsDependencies": ["object-path", "apexcharts", "clipboard.js", "prismjs", "moment", "sweetalert2", "qrious"], "index": "src/index.html", "polyfills": ["@angular/localize/init", "zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/swiper/swiper-bundle.css", "src/styles.scss"], "scripts": [], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development-second": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "atom:build:production"}, "development": {"buildTarget": "atom:build:development"}, "development-second": {"buildTarget": "atom:build:development-second"}, "staging": {"buildTarget": "atom:build:staging"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "atom:build"}}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "./jest.config.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "7c1ccabd-60aa-4fcd-be08-a2a3e04e38e6", "schematicCollections": ["@angular-eslint/schematics"]}}