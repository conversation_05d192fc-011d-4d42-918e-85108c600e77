# 🔄 Branch Synchronization Setup Guide

## 📋 Overview

This guide explains the automated branch synchronization system for our multi-branch workflow. The system uses GitLab CI with modular pipeline configuration to manage branch synchronization safely and efficiently.

### **🔄 Branch Flow:**
```
development ──(AUTO)──→ development-second
     │
     └──(MANUAL)──→ staging ──(MANUAL)──→ main (PRODUCTION)
```

### **🎯 Automation Strategy:**
- **✅ Automatic**: `development` → `development-second` (safe for continuous development)
- **⚠️ Manual**: `development` → `staging` (controlled staging deployment)
- **⚠️ Manual**: `staging` → `main` (production safety gate)

## 🚀 GitLab CI Automation (Recommended)

### **1. Setup GitLab CI Variables**

Go to **GitLab Project Settings** → **CI/CD** → **Variables** and add:

| Variable | Value | Protected | Masked |
|----------|-------|-----------|--------|
| `CI_PUSH_TOKEN` | Personal Access Token with `write_repository` scope | ✅ | ✅ |

**Note**: The modular CI/CD structure is already configured and ready to use.

**How to create Personal Access Token:**
1. Go to GitLab **User Settings** → **Access Tokens**
2. Create token with scopes: `write_repository`, `read_repository`
3. Copy token and add to CI/CD variables

### **2. Enable GitLab CI**

The project now uses a modular CI/CD structure. The main `.gitlab-ci.yml` already includes:

```yaml
# Include modular pipeline configurations
include:
  # Branch synchronization pipeline
  - local: '.gitlab/ci/branch-sync.yml'
```

**CI/CD Structure:**
```
.gitlab/ci/
├── branch-sync.yml          # Branch synchronization (active)
├── testing.yml.example      # Testing pipeline template
├── linting.yml.example      # Linting pipeline template
└── README.md               # CI/CD documentation
```

**Modular Include Structure:**
```yaml
# .gitlab-ci.yml
include:
  - local: '.gitlab/ci/branch-sync.yml'  # ✅ Active
  # - local: '.gitlab/ci/testing.yml'    # Future expansion
  # - local: '.gitlab/ci/linting.yml'    # Future expansion
```

### **3. GitLab CI Jobs Overview**

The branch synchronization uses a modular GitLab CI structure with the following jobs:

#### **📋 Available Jobs:**
| Job Name | Trigger | Source → Target | Purpose |
|----------|---------|-----------------|---------|
| `sync-to-dev-second` | **Automatic** | `development` → `development-second` | Continuous development sync |
| `sync-to-staging` | **Manual** | `development` → `staging` | Staging deployment |
| `sync-to-main` | **Manual** | `staging` → `main` | Production deployment |
| `reverse-sync-dev-second-to-dev` | **Manual** | `development-second` → `development` | Emergency reverse sync |

#### **🔄 Job Execution Flow:**

**Automatic Sync (on push to development):**
```
Push to development → GitLab CI Pipeline → sync-to-dev-second (auto) → Vercel Preview
```
- ✅ **Zero manual intervention** required
- ✅ **Safe for continuous development**
- ✅ **Immediate feedback** via Vercel preview

**Manual Staging Sync:**
```
GitLab Pipeline → Click "Play" on sync-to-staging → development merged to staging → Vercel Preview
```
- ⚠️ **Manual trigger** in GitLab CI/CD interface
- ⚠️ **Controlled staging deployment**
- ⚠️ **Staging verification** before production

**Manual Production Sync:**
```
GitLab Pipeline → Click "Play" on sync-to-main → staging merged to main → Vercel Production
```
- ⚠️ **Manual trigger** with extra safety
- ⚠️ **Production deployment**
- ⚠️ **Final safety gate**

**Emergency Reverse Sync:**
```
GitLab Pipeline → Click "Play" on reverse-sync-dev-second-to-dev → cherry-pick commits → development updated
```
- 🚨 **Emergency use only** - When development-second has critical fixes
- ⚠️ **Manual trigger** from development-second pipeline
- ✅ **Cherry-pick approach** - Safe and selective commit sync
- ✅ **Conflict detection** - Stops if conflicts detected

## 🎮 Manual Job Triggers (GitLab CI)

### **📍 How to Trigger Manual Sync Jobs:**

#### **1. Navigate to GitLab Pipeline:**
```
GitLab Project → CI/CD → Pipelines → Latest Pipeline (development branch)
```

#### **2. Locate Manual Jobs:**
- **`sync-to-staging`** - Shows with ▶️ play button
- **`sync-to-main`** - Shows with ▶️ play button

#### **3. Execute Manual Sync:**

**For Staging Deployment:**
```
1. Click ▶️ on "sync-to-staging" job
2. Confirm execution
3. Monitor job logs for success
4. Verify staging deployment in Vercel
```

**For Production Deployment:**
```
1. Ensure staging is tested and verified
2. Click ▶️ on "sync-to-main" job
3. Confirm execution (extra caution!)
4. Monitor job logs for success
5. Verify production deployment in Vercel
```

### **✅ Post-Sync Validation:**

#### **Verify Sync Success:**
```bash
# Check target branch has latest commits
git fetch origin
git log origin/staging --oneline -5    # For staging sync
git log origin/main --oneline -5       # For production sync
```

#### **Verify Deployments:**
- **Staging**: Check Vercel preview URL
- **Production**: Check production URL
- **Functionality**: Test key features work correctly

## 🔄 Reverse Sync (Parallel Development & Emergency)

### **📍 When to Use Reverse Sync:**

#### **🚀 Parallel Development (Primary Use Case):**
- **👥 Multi-team development** - Team A di development, Team B di development-second
- **🔧 Shared component updates** - Enhanced components yang dibutuhkan team lain
- **📦 Feature coordination** - Feature B selesai di development-second, perlu di development
- **🔄 Cross-team collaboration** - Sinkronisasi fitur antar development teams

#### **🚨 Emergency Scenarios:**
- **Critical hotfix** applied directly to development-second
- **Manual fix** for urgent production issue in development-second
- **Conflict resolution** that was done in development-second
- **External contribution** that landed in development-second first

### **💡 Parallel Development Strategy:**

**Common Scenario:**
```
Development Team 1 (development):        Feature A
Development Team 2 (development-second): Feature B + Enhanced shared components
```

**Solution Flow:**
```
1. Team 2 completes Feature B + shared component enhancements
2. Team 1 needs shared component updates for Feature A
3. Trigger reverse sync: development-second → development
4. Auto-sync propagates: development → development-second
5. Both teams now have Feature A + Feature B + enhanced components
```

### **⚠️ Usage Guidelines:**

- **Coordinate with teams** - Communicate before triggering reverse sync
- **Review commits** - Always check what will be synced before triggering
- **Test integration** - Verify compatibility after reverse sync
- **Conflict handling** - Pipeline stops if conflicts detected, requires manual resolution

### **🎮 How to Trigger Reverse Sync:**

#### **Step 1: Navigate to development-second Pipeline**
```
GitLab Project → CI/CD → Pipelines → development-second branch → Latest Pipeline
```

#### **Step 2: Trigger Reverse Sync Job**
```
1. Locate "reverse-sync-dev-second-to-dev" job
2. Click ▶️ play button
3. Monitor job execution logs
4. Verify commits are cherry-picked successfully
```

#### **Step 3: Handle Conflicts (if any)**
```
If pipeline fails due to conflicts:
1. Note the conflicting commit hash from logs
2. Perform manual cherry-pick locally:
   git checkout development
   git cherry-pick <commit-hash>
   # Resolve conflicts manually
   git add . && git cherry-pick --continue
   git push origin development
```

#### **Step 4: Verify Auto-Sync**
```
After reverse sync completes:
1. Check development branch has the new commits
2. Verify auto-sync: development → development-second works normally
3. Monitor for any sync issues
4. Coordinate with other teams about updated features
```

### **🤝 Parallel Development Coordination**

#### **Best Practices for Multi-Team Development:**

**1. Communication Protocol:**
```
- Announce shared component changes in team chat
- Coordinate reverse sync timing with affected teams
- Document feature dependencies and integration points
- Schedule regular sync meetings for major updates
```

**2. Development Workflow:**
```bash
# Team A (development branch)
git checkout development
git checkout -b feat/feature-a
# ... develop Feature A ...

# Team B (development-second branch)
git checkout development-second
git checkout -b feat/feature-b-shared-components
# ... develop Feature B + enhance shared components ...

# When Team A needs shared component updates:
# 1. Team B pushes to development-second
# 2. Trigger reverse sync via GitLab CI
# 3. Team A gets updated shared components
# 4. Continue Feature A development with enhanced components
```

**3. Conflict Prevention:**
```
- Avoid simultaneous edits to same files
- Use feature flags for incomplete features
- Establish clear ownership for shared components
- Regular integration testing after sync
```

**4. Staging Coordination:**
```
- Sync to staging after both features are ready
- Comprehensive testing with integrated features
- Coordinate deployment timing between teams
```

### **🛡️ Safety Features:**

- **✅ Commit validation** - Shows which commits will be synced
- **✅ Conflict detection** - Stops process if conflicts found
- **✅ Selective sync** - Only syncs commits not in development
- **✅ Audit trail** - Clear logs of what was synced
- **✅ Rollback friendly** - Changes can be reverted if needed

## 🧪 Local Testing (Start Here!)

### **1. Prerequisites Check**

```bash
# Check current branch
git branch --show-current

# Check git status (should be clean)
git status

# Check access to all branches
git branch -a
```

### **2. Make Script Executable**

```bash
chmod +x scripts/sync-branches.sh
```

### **3. Test with Dry Run (SAFE)**

#### **Test development branches sync:**
```bash
# This shows what WOULD happen without actually doing it
./scripts/sync-branches.sh --dry-run

# Expected output:
# ℹ️  Starting branch synchronization (mode: dev-only)
# ⚠️  DRY RUN MODE - No actual changes will be made
# ⚠️  DRY RUN: Would sync development → development-second
# ⚠️  DRY RUN: Would sync development → staging
# ✅ Branch synchronization completed!
```

#### **Test full sync (including production):**
```bash
# Test what would happen with production sync
./scripts/sync-branches.sh --mode=full --dry-run

# Expected output includes:
# ⚠️  DRY RUN: Would sync staging → main (PRODUCTION)
```

### **4. Test Real Sync (Development Only)**

#### **Create test commit first:**
```bash
# Make sure you're on development
git checkout development

# Create a test file
echo "# Test sync $(date)" > test-sync.md
git add test-sync.md
git commit -m "CHORE: test branch sync functionality"
```

#### **Execute development sync:**
```bash
# Sync development → development-second only
./scripts/sync-branches.sh --mode=dev-only

# Expected output:
# ℹ️  Starting branch synchronization (mode: dev-only)
# ℹ️  Phase 1: Development-second synchronization
# ℹ️  Syncing development → development-second
# ✅ Successfully synced development → development-second
# ✅ Branch synchronization completed!
# 💡 To sync to staging: ./scripts/sync-branches.sh --mode=staging
```

### **5. Verify Sync Results**

```bash
# Check development-second has the test commit
git checkout development-second
git log --oneline -5

# Check staging has the test commit
git checkout staging
git log --oneline -5

# Both should show your test commit
```

### **6. Cleanup Test**

```bash
# Remove test file from all branches
git checkout development
git rm test-sync.md
git commit -m "CHORE: cleanup test sync file"

# Sync cleanup to other branches
./scripts/sync-branches.sh --mode=dev-only
```

## 🛠️ Local Script Reference

### **Usage Examples**

#### **Development-second only (safe):**
```bash
# Dry run first (always recommended)
./scripts/sync-branches.sh --dry-run

# Execute sync: development → development-second only
./scripts/sync-branches.sh --mode=dev-only
```

#### **Include staging:**
```bash
# Dry run first
./scripts/sync-branches.sh --mode=staging --dry-run

# Execute sync: development → development-second → staging
./scripts/sync-branches.sh --mode=staging
```

#### **Full sync including production:**
```bash
# Dry run first (IMPORTANT for production)
./scripts/sync-branches.sh --mode=full --dry-run

# Execute full sync (includes main/production)
./scripts/sync-branches.sh --mode=full
```

#### **Help:**
```bash
./scripts/sync-branches.sh --help
```

## 📋 Workflow Examples

### **Scenario 1: Regular Development**

```bash
# 1. Work on development branch
git checkout development
git add .
git commit -m "FEAT: new feature implementation"
git push origin development

# 2. ✅ GitLab CI automatically syncs: development → development-second
#    - Pipeline triggers automatically
#    - sync-to-dev-second job runs
#    - Vercel preview deployment for development-second

# 3. ✅ Verify development-second preview deployment
#    - Check Vercel preview URL
#    - Test functionality

# 4. ⚠️ Manually trigger staging sync:
#    - Go to GitLab CI Pipeline
#    - Click ▶️ on "sync-to-staging" job
#    - Monitor job completion

# 5. ✅ Verify staging preview deployment
#    - Check staging Vercel preview URL
#    - Perform staging tests

# 6. ⚠️ Manually trigger production sync when ready:
#    - Go to GitLab CI Pipeline
#    - Click ▶️ on "sync-to-main" job
#    - Monitor production deployment
```

### **Scenario 2: Documentation/Tooling Updates**

```bash
# 1. Commit documentation changes
git checkout development
git add .
git commit -m "CHORE: enhance branch sync documentation"
git push origin development

# 2. ✅ Auto-sync to development-second (immediate)
#    - GitLab CI pipeline triggers
#    - Documentation available in development-second preview

# 3. ⚠️ Manual sync to staging when ready
#    - Review documentation in development-second
#    - Trigger sync-to-staging job manually
#    - Verify in staging environment

# 4. ⚠️ Manual sync to production when staging verified
#    - Confirm documentation is correct in staging
#    - Trigger sync-to-main job manually
#    - Documentation live in production
```

### **Scenario 3: Emergency Local Sync**

```bash
# If GitLab CI is down, use local script
./scripts/sync-branches.sh --mode=dev-only

# For production (with confirmation prompt)
./scripts/sync-branches.sh --mode=full
```

## ⚠️ Important Notes

### **Production Safety**
- **Main branch sync is ALWAYS manual** (never automatic)
- **Vercel production deployment** only triggers from main
- **Double confirmation** required for production sync

### **Vercel Integration**
- **All branches** trigger Vercel preview deployments
- **Only main** triggers production deployment
- **Preview URLs** available for testing before production

### **Conflict Resolution**
- **GitLab CI will fail** if merge conflicts occur
- **Use local script** to resolve conflicts manually
- **Always test in staging** before production sync

## 🔧 Troubleshooting

### **Local Testing Issues**

#### **Script Permission Denied**
```bash
# Make script executable
chmod +x scripts/sync-branches.sh

# Check permissions
ls -la scripts/sync-branches.sh
# Should show: -rwxr-xr-x
```

#### **Working Directory Not Clean**
```bash
# Check what's uncommitted
git status

# Commit or stash changes
git add .
git commit -m "WIP: save current work"
# OR
git stash
```

#### **Branch Not Found**
```bash
# Fetch all branches
git fetch origin

# Check available branches
git branch -a

# Create missing branch if needed
git checkout -b development-second origin/development-second
```

#### **Merge Conflicts During Test**
```bash
# If sync fails due to conflicts
git status  # See conflicted files

# Resolve conflicts manually
# Edit files, then:
git add .
git commit -m "FIX: resolve merge conflicts"

# Continue with sync
./scripts/sync-branches.sh --mode=dev-only
```

### **GitLab CI Issues**

#### **Pipeline Creation Fails**
```bash
# Error: "Unable to create pipeline"
# Cause: Include local file issues or YAML syntax errors

# Solution:
1. Check .gitlab-ci.yml syntax
2. Verify .gitlab/ci/branch-sync.yml exists
3. Check include path format: 'local: .gitlab/ci/branch-sync.yml'
4. Validate YAML with GitLab CI Lint tool
```

#### **Manual Jobs Not Visible**
```bash
# Issue: sync-to-staging or sync-to-main jobs don't appear
# Cause: Jobs only show on development branch pipelines

# Solution:
1. Ensure you're viewing development branch pipeline
2. Check job 'only: - development' configuration
3. Verify include file is properly loaded
```

#### **GitLab CI Job Fails**
```bash
# Check CI logs in GitLab Project → CI/CD → Pipelines
# Common issues:
# 1. Missing CI_PUSH_TOKEN variable
# 2. Token expired or wrong permissions
# 3. Merge conflicts in CI environment
# 4. Branch protection rules
# 5. Alpine git installation issues
```

#### **Include Local File Issues**
```bash
# Error: "jobs:sync-to-dev-second:script config should be a string or array"
# Cause: Include local file not loading properly

# Solution 1: Check file path
- Verify: .gitlab/ci/branch-sync.yml exists
- Check: include path uses forward slashes
- Format: local: '.gitlab/ci/branch-sync.yml'

# Solution 2: Validate YAML syntax
- Use GitLab CI Lint: Project → CI/CD → Editor → Validate
- Check indentation (use spaces, not tabs)
- Verify quotes around environment variables

# Solution 3: Fallback approach
- Comment out include section temporarily
- Use backup jobs in main .gitlab-ci.yml
- Debug include issues separately
```

#### **Environment Variable Issues**
```bash
# Error: YAML parsing issues with commit messages containing colons
# Cause: GitLab CI YAML parser conflicts with colon in strings

# Solution: Use proper quoting
# ❌ Wrong:
- COMMIT_MSG="CHORE: message"

# ✅ Correct:
- 'export COMMIT_MSG="CHORE: message"'
```

#### **Token Issues**
```bash
# Verify token in GitLab:
# Project → Settings → CI/CD → Variables
# Should see: CI_PUSH_TOKEN [masked]

# Test token permissions locally:
git clone https://oauth2:<EMAIL>/maxxi-agro/atom.git test-clone
cd test-clone
git push origin development  # Should work
```

### **General Merge Conflicts**
```bash
# Manual resolution required
git checkout target-branch
git merge source-branch

# If conflicts:
# 1. Edit conflicted files
# 2. Remove conflict markers (<<<<< ===== >>>>>)
# 3. Test the resolution
git add .
git commit -m "FIX: resolve merge conflicts between source-branch and target-branch"
git push origin target-branch
```

### **Emergency Rollback**
```bash
# If something goes wrong, rollback specific branch
git checkout branch-name
git reset --hard HEAD~1  # Go back 1 commit
git push origin branch-name --force-with-lease

# For main branch (CAREFUL!)
git checkout main
git reset --hard SAFE_COMMIT_HASH
git push origin main --force-with-lease
```

## 📊 Benefits

### **Automation Benefits:**
- ✅ **Consistent sync process**
- ✅ **Reduced manual errors**
- ✅ **Audit trail in GitLab**
- ✅ **Integration with Vercel deployments**

### **Safety Benefits:**
- ✅ **Manual gate for production**
- ✅ **Preview deployments for testing**
- ✅ **Rollback capabilities**
- ✅ **Local backup option**

## 📋 Quick Reference

### **🚀 Common Workflows:**

#### **Regular Development:**
```bash
# 1. Work on development
git checkout development
git add . && git commit -m "FEAT: new feature"
git push origin development

# 2. Auto-sync happens: development → development-second
# 3. Manual staging: Click ▶️ sync-to-staging in GitLab
# 4. Manual production: Click ▶️ sync-to-main in GitLab
```

#### **Emergency Local Sync:**
```bash
# If GitLab CI is down
./scripts/sync-branches.sh --mode=dev-only    # Safe
./scripts/sync-branches.sh --mode=staging     # Include staging
./scripts/sync-branches.sh --mode=full        # Include production
```

### **🔧 Decision Tree:**

```
Need to sync?
├── development → development-second: ✅ Automatic (just push)
├── development → staging: ⚠️ Manual (GitLab CI ▶️ button)
├── staging → main: ⚠️ Manual (GitLab CI ▶️ button)
└── GitLab CI down: 🛠️ Local script (./scripts/sync-branches.sh)
```

### **⚡ Quick Commands:**
```bash
# Check current status
git status && git branch --show-current

# Verify sync results
git fetch origin && git log origin/TARGET_BRANCH --oneline -5

# Local emergency sync
./scripts/sync-branches.sh --dry-run  # Always test first
./scripts/sync-branches.sh --mode=dev-only

# Troubleshoot GitLab CI
# → Go to: Project → CI/CD → Pipelines → View logs
```

### **🎯 Key Points:**
- ✅ **development-second**: Always automatic
- ⚠️ **staging**: Manual trigger required
- ⚠️ **main**: Manual trigger required
- 🛠️ **Local script**: Emergency backup option
- 📋 **Validation**: Always verify sync results

---

**Setup Complete!** 🎉

Your branch synchronization is now automated with production safety gates and comprehensive troubleshooting support.
