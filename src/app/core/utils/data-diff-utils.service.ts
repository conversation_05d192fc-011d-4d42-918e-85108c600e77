import { Injectable } from '@angular/core';
import { ICardDetailBody } from '@models/card.model';

@Injectable({ providedIn: 'root' })
export class DataDiffUtilsService {
  generateUpdateValue(data: any[], newData: any): any[] {
    return data.map((val) => {
      const objectValue: any = {
        key: val.key,
        label: val.label,
        value: val.value,
        ...(val.title && { title: val.title }),
      };

      if (typeof val.key === 'string' && newData.hasOwnProperty(val.key)) {
        if (val.value !== newData[val.key]) {
          objectValue.new = newData[val.key];
        }
      } else if (Array.isArray(val.key)) {
        const relevantKeys = val.key.filter((k: string) => newData.hasOwnProperty(k));
        if (relevantKeys.length) {
          const newVals = val.key.map((k: string, i: number) => newData[k] ?? val.value.split(', ')[i]);
          if (val.value !== newVals.join(', ')) {
            objectValue.new = newVals.join(', ');
          }
        }
      }

      return objectValue;
    });
  }

  generateUpdateEditData(oldData: ICardDetailBody[], newData: ICardDetailBody[]): ICardDetailBody[] {
    return oldData.map((item, index) => {
      const diff: ICardDetailBody = { ...item };
      if (!Array.isArray(item.value) && item.value !== newData[index].value) {
        diff.new = newData[index].value;
      }
      return diff;
    });
  }

  generateSideDocument(key: string, newData: any, current: any): { key: string; value: string }[] {
    const result = [current];
    if (newData.hasOwnProperty(key) && newData[key] && newData[key] !== current.value) {
      result.push({ key: 'new', value: newData[key] });
    }
    return result;
  }
}
