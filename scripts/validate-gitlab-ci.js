#!/usr/bin/env node

/**
 * GitLab CI Validation Script (Cross-platform)
 * Works on Windows, macOS, and Linux
 * Usage: node scripts/validate-gitlab-ci.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(type, message) {
    const timestamp = new Date().toLocaleTimeString();
    switch (type) {
        case 'success':
            console.log(`${colors.green}✅ [${timestamp}] ${message}${colors.reset}`);
            break;
        case 'error':
            console.log(`${colors.red}❌ [${timestamp}] ${message}${colors.reset}`);
            break;
        case 'warning':
            console.log(`${colors.yellow}⚠️  [${timestamp}] ${message}${colors.reset}`);
            break;
        case 'info':
            console.log(`${colors.blue}ℹ️  [${timestamp}] ${message}${colors.reset}`);
            break;
        default:
            console.log(`[${timestamp}] ${message}`);
    }
}

function checkFileExists(filePath) {
    if (fs.existsSync(filePath)) {
        log('success', `File exists: ${filePath}`);
        return true;
    } else {
        log('error', `File missing: ${filePath}`);
        return false;
    }
}

function validateYamlSyntax(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // Basic YAML validation checks
        const fileName = path.basename(filePath);

        // Check for tabs
        if (content.includes('\t')) {
            log('warning', `Found tabs in ${fileName}, use spaces instead`);
        }

        // Check for trailing whitespace
        const lines = content.split('\n');
        const trailingLines = lines
            .map((line, index) => ({ line, number: index + 1 }))
            .filter(item => item.line.match(/ +$/));

        if (trailingLines.length > 0) {
            log('warning', `Trailing whitespace in ${fileName} on lines: ${trailingLines.map(item => item.number).join(', ')}`);
        }

        // Check for basic YAML structure
        if (content.includes('stages:') || content.includes('include:') || content.includes('script:')) {
            log('success', `YAML structure looks valid: ${fileName}`);
        } else {
            log('warning', `${fileName} might not be a valid GitLab CI file`);
        }

        return true;
    } catch (error) {
        log('error', `Error reading ${path.basename(filePath)}: ${error.message}`);
        return false;
    }
}

function validateIncludedFiles(mainConfigPath) {
    try {
        const content = fs.readFileSync(mainConfigPath, 'utf8');

        // Find active include lines (not commented)
        const lines = content.split('\n');
        const includeFiles = [];

        for (const line of lines) {
            // Skip commented lines
            if (line.trim().startsWith('#')) continue;

            // Look for local includes
            const match = line.match(/local:\s*['"]([^'"]+)['"]/);
            if (match) {
                includeFiles.push(match[1]);
            }
        }

        if (includeFiles.length > 0) {
            log('info', 'Validating included files...');

            for (const filePath of includeFiles) {
                if (!checkFileExists(filePath)) {
                    return false;
                }
                if (!validateYamlSyntax(filePath)) {
                    return false;
                }
            }
        }
        return true;
    } catch (error) {
        log('error', `Error validating includes: ${error.message}`);
        return false;
    }
}

function runGitLabCILint() {
    try {
        log('info', 'Running GitLab CI lint...');

        // Check if glab is available
        try {
            execSync('glab --version', { stdio: 'pipe' });
        } catch (error) {
            log('warning', 'glab CLI not found, skipping GitLab CI lint');
            log('info', 'Install glab from: https://github.com/profclems/glab');
            return true; // Don't fail if glab is not available
        }

        const result = execSync('glab ci lint', { encoding: 'utf8', stdio: 'pipe' });
        log('success', 'GitLab CI configuration is valid');
        return true;
    } catch (error) {
        log('error', `GitLab CI lint failed: ${error.message}`);
        return false;
    }
}

function validatePackageJson() {
    if (fs.existsSync('package.json')) {
        try {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            log('success', `Package: ${packageJson.name} v${packageJson.version}`);
            return true;
        } catch (error) {
            log('error', `Invalid package.json: ${error.message}`);
            return false;
        }
    } else {
        log('warning', 'package.json not found');
        return true;
    }
}

function main() {
    console.log(`${colors.cyan}🔍 GitLab CI Validation Script${colors.reset}`);
    console.log(`${colors.cyan}================================${colors.reset}\n`);

    let allValid = true;

    // Check main CI file
    log('info', 'Checking main GitLab CI file...');
    if (!checkFileExists('.gitlab-ci.yml')) {
        process.exit(1);
    }

    if (!validateYamlSyntax('.gitlab-ci.yml')) {
        allValid = false;
    }

    // Check included files
    log('info', 'Validating included files...');
    if (!validateIncludedFiles('.gitlab-ci.yml')) {
        allValid = false;
    }

    // Check CI directory files
    if (fs.existsSync('.gitlab/ci')) {
        log('success', 'Found .gitlab/ci directory');
        const ciFiles = fs.readdirSync('.gitlab/ci')
            .filter(file => file.endsWith('.yml'))
            .map(file => path.join('.gitlab/ci', file));

        for (const file of ciFiles) {
            if (!validateYamlSyntax(file)) {
                allValid = false;
            }
        }
    } else {
        log('warning', '.gitlab/ci directory not found');
    }

    // Validate package.json
    log('info', 'Checking package.json...');
    if (!validatePackageJson()) {
        allValid = false;
    }

    // Run GitLab CI lint
    if (!runGitLabCILint()) {
        allValid = false;
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    if (allValid) {
        log('success', 'All validations passed! 🚀');
        log('info', 'Your GitLab CI configuration is ready to deploy');
        console.log('\n💡 Next steps:');
        console.log('   1. Push to feature branch for testing');
        console.log('   2. Check pipeline: https://gitlab.com/maxxi-agro/atom/-/pipelines');
        console.log('   3. Use GitLab CI Lint: https://gitlab.com/maxxi-agro/atom/-/ci/lint');
    } else {
        log('error', 'Some validations failed. Please fix the issues above.');
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { validateYamlSyntax, checkFileExists, validateIncludedFiles };
