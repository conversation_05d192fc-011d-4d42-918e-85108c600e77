{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "strict": true, "strictPropertyInitialization": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "module": "ES2022", "useDefineForClassFields": false, "lib": ["ES2022", "dom"], "paths": {"@utils/*": ["src/app/core/utils/*"], "@directives/*": ["src/app/directives/*"], "@environments/*": ["src/environments/*"], "@guards/*": ["src/app/guards/*"], "@models/*": ["src/app/models/*"], "@pages/*": ["src/app/pages/*"], "@services/*": ["src/app/services/*"], "@shared/*": ["src/app/shared/*"], "@config/*": ["src/app/config/*"], "@metronic/*": ["src/app/_metronic/*"]}}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true, "allowSyntheticDefaultImports": true, "extendedDiagnostics": {"checks": {"optionalChainNotNullable": "suppress", "nullishCoalescingNotNullable": "suppress"}}}}