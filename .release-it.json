{"git": {"commitsPath": ".", "commitMessage": "CHORE: Release v${version}", "push": true, "pushRepo": "**************:maxxi-agro/atom.git", "requireCommits": true, "requireUpstream": false}, "npm": {"publish": false}, "hooks": {"before:release": "npm run build:scripts", "after:release": "node scripts/dist/archive-changelog.js"}, "plugins": {"@release-it/conventional-changelog": {"infile": "CHANGELOG.md", "header": "# Backoffice Changelog", "preset": {"name": "conventionalcommits", "types": [{"type": "feat", "section": "Features:"}, {"type": "fix", "section": "Bug Fixes:"}]}, "ignoreRecommendedBump": true}, "@release-it/bumper": {"out": ["src/environments/environment.ts", "src/environments/environment.dev.ts", "src/environments/environment.staging.ts", "src/environments/environment.prod.ts"]}}}