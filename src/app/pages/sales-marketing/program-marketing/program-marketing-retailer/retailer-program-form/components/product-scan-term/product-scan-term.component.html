<h3 class="mb-10 fw-bold">Ketentuan <PERSON> Produk</h3>
<ng-container [ngTemplateOutlet]="(isLoadingSubject | async) ? loaderTpl : sectionTpl"></ng-container>

<ng-template #sectionTpl>
  <app-note-view-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? '-'" />

  <div class="form-group" [formGroup]="form">
    <div class="d-flex flex-wrap w-100 mb-6">
      <label class="required col-form-label col-12 col-lg-5">Aturan Scan QR</label>
      <div class="col-12 col-lg-7">
        <div class="form-check text-capitalize p-0 ms-n5">
          <app-input-select-radio
            [disable]="isDisableEdit()"
            [useLabel]="false"
            [useColumn]="true"
            [options]="optionsRadioRuleScan"
            [formControlName]="'rule_scan'"
            ngDefaultControl
          />
        </div>
      </div>
    </div>

    <!-- general note view default show -->
    <ng-container
      *ngIf="!TypeTargetScan?.value"
      [ngTemplateOutlet]="noteViewInfoTpl"
      [ngTemplateOutletContext]="{ data: 'Produk bisa ditambah setelah memilih tipe target' }"
    ></ng-container>
    <ng-container
      *ngIf="TypeTargetScan?.value && utils.isObjectEmpty(RuleTargetScan?.value)"
      [ngTemplateOutlet]="noteViewInfoTpl"
      [ngTemplateOutletContext]="{ data: 'Produk bisa ditambah setelah memilih aturan target' }"
    ></ng-container>

    <!-- show if tipe target (ketentuan target scan): volume/tonase -->
    <ng-container
      *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.VOLUME && !utils.isObjectEmpty(RuleTargetScan?.value)"
      [ngTemplateOutlet]="noteViewInfoTpl"
      [ngTemplateOutletContext]="{ data: 'Silakan pilih produk dengan satuan volume/tonase yang sama.' }"
    ></ng-container>

    <div class="row gx-0 mb-6">
      <label class="required col-form-label col-12 col-lg-5">Produk Discan</label>
      <div class="col-12 col-lg-7">
        <div class="ms-n2">
          <app-add-product-scan
            #inputProductScanRef
            [disabled]="!TypeTargetScan?.value || utils.isObjectEmpty(RuleTargetScan?.value) || !RuleScan?.value || isDisableEdit()"
            [typeTargetScan]="TypeTargetScan?.value"
            (selectionOutput)="handleSelectedProduct($event)"></app-add-product-scan>
        </div>
      </div>
    </div>

    <!-- show only if tipe target (ketentuan target scan): jumlah qr -->
    <!-- Product Conversion -->
    <ng-container *ngIf="isHasConversion()">
      <div class="d-flex flex-wrap w-100 mb-6 align-items-center animate__animated animate__fadeIn">
        <label class="col-form-label col-12 col-lg-5">
          <span class="d-block">Konversi Satuan/Penyetaraan Produk <i class="text-gray-700">(Opsional)</i></span>
          <span class="text-gray-700 fs-8">Hanya untuk variant produk di brand yang sama.</span>
        </label>
        <div class="col-12 col-lg-7">
          <div class="ms-n2">
              <app-add-product-conversion #inputProductConversionRef [data]="dataProductScan" [disable]="isDisableEdit()" (conversionForm)="handleEmittedConversionForm($event)" />
          </div>
        </div>
      </div>
    </ng-container>
    <!-- Product Conversion End -->

    <ng-container
      *ngIf="TypeTargetScan?.value && !utils.isObjectEmpty(RuleTargetScan?.value) && !RuleScan?.value"
      [ngTemplateOutlet]="noteViewInfoTpl"
      [ngTemplateOutletContext]="{ data: 'Produk bisa ditambah setelah memilih aturan scan QR.' }"
    ></ng-container>

    <!-- Target Program -->
    <ng-container *ngIf="isScanQrRule(EnumProgramMarketingScanRule.ACCUMULATION_PRODUCTS)">
      <!-- single target -->
      <ng-container *ngIf="!isMultiLevelTarget() && TargetScanQrForm.value.length > 0">
        <div class="row gx-0 mb-6 align-items-center">
          <ng-container
            [ngTemplateOutlet]="singleTargetProgramInputTpl"
            [ngTemplateOutletContext]="{ data: { fg: form, ctrlName: 'target_program', deliveryUnit: getSingleTargetProgramUnit() } }"
          ></ng-container>
        </div>
      </ng-container>

      <!-- multilevel target -->
      <ng-container *ngIf="isMultiLevelTarget()" formArrayName="multilevel_target">
        <ng-container *ngIf="!!MultiLevelTargetForm?.value.length">
          <div *ngFor="let num of Array.from({ length: getNumberOfTargetLevel() }); let i = index" class="row gx-0 mb-6 align-items-center">
            <h5 class="fw-bold col-12">Tingkat {{ i + 1 }}</h5>
            <div class="row gx-0 mb-6 align-items-center">
              <ng-container
                [ngTemplateOutlet]="singleTargetProgramInputTpl"
                [ngTemplateOutletContext]="{ data: { fg: getMultiLevelTargetFormGroup(i), ctrlName: 'value', deliveryUnit: getSingleTargetProgramUnit() }, index: i }"
              ></ng-container>
            </div>
          </div>
        </ng-container>
      </ng-container>
      <!-- multilevel target end -->
    </ng-container>
    <!-- Target Program End -->

    <!-- Target Scan QR -->
    <ng-container *ngIf="isScanQrRule(EnumProgramMarketingScanRule.ALL_PRODUCTS)">
      <!-- single target -->
      <ng-container *ngIf="!isMultiLevelTarget() && TargetScanQrForm.value.length > 0">
        <h5 class="mb-6">Target Scan QR</h5>
        <ng-container formArrayName="target_scan_qr">
          <div class="row gx-0 mb-6 align-items-center" *ngFor="let ctrl of TargetScanQrForm.controls; let i = index" [formGroupName]="i">
            <label class="required col-form-label col-12 col-lg-5">{{ ctrl.value.name }}</label>
            <div class="col-12 col-lg-7">
              <div class="input-group input-group-solid">
                <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.PRICE_NOMINAL" class="input-group-text text-capitalize fs-8 text-gray-700">Rp</span>
                <input
                  class="border-start-0 form-control form-control-solid"
                  placeholder="Silahkan input target"
                  [formControlName]="'value'"
                  appNumberInputFormat
                  ngDefaultControl
                />
                <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.QTY_QR" class="input-group-text text-capitalize fs-8 text-gray-700">QR</span>
                <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.VOLUME" class="input-group-text text-capitalize fs-8 text-gray-700">
                  {{ ctrl.value.delivery_unit }}
                </span>
              </div>
            </div>
          </div>
        </ng-container>
      </ng-container>
      <!-- single target end -->

      <!-- multilevel target -->
      <ng-container *ngIf="isMultiLevelTarget()">
        <ng-container formArrayName="multilevel_target">
          <div class="row gx-0 mb-6" *ngFor="let ctrl of MultiLevelTargetForm.controls; let i = index" [formGroupName]="i">
            <h5 class="fw-bold col-12">Tingkat {{ i + 1 }}</h5>
            <ng-container formArrayName="products">
              <div
                class="row gx-0 mb-6 align-items-center"
                *ngFor="let productCtrl of getMultiLevelTargetProducts(i).controls; let productIndex = index"
                [formGroupName]="productIndex"
              >
                <label class="required col-form-label col-12 col-lg-5">{{ productCtrl.value.name }}</label>
                <fieldset [disabled]="isDisableEdit()" class="col-12 col-lg-7">
                  <div class="input-group input-group-solid">
                    <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.PRICE_NOMINAL" class="input-group-text text-capitalize fs-8 text-gray-700">Rp</span>
                    <input
                      [formControlName]="'value'"
                      class="border-start-0 form-control form-control-solid border-end-0"
                      placeholder="Silahkan input target"
                      appNumberInputFormat
                      ngDefaultControl
                    />
                    <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.QTY_QR" class="input-group-text text-capitalize fs-8 text-gray-700">QR</span>
                    <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.VOLUME" class="input-group-text text-capitalize fs-8 text-gray-700">{{
                      productCtrl.value.delivery_unit
                    }}</span>
                  </div>
                </fieldset>
              </div>
            </ng-container>
          </div>
        </ng-container>
      </ng-container>
      <!-- multilevel target end -->
    </ng-container>
    <!-- Target Scan QR End -->

    <ng-template #singleTargetProgramInputTpl let-data="data" let-index="index">
      <label class="required col-form-label col-12 col-lg-5">Target Program</label>
      <fieldset [disabled]="isMultiLevelTarget() ? handleDisableInputMultilevelTarget(index, data.ctrlName) : (isDisableEdit() && isAddedNewProductScan())" class="col-12 col-lg-7" [formGroup]="data.fg">
        <div
          class="input-group input-group-solid ms-n2"
          [ngClass]="{
            'border border-danger': data.fg.get('value')?.dirty && data.fg.get('value')?.hasError('notHigherThanPrevious') || data.fg.get(data.ctrlName).hasError('min'),
          }"
        >
          <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.PRICE_NOMINAL" class="input-group-text text-capitalize fs-8 text-gray-700">Rp</span>
          <input
            class="border-start-0 form-control form-control-solid border-end-0"
            [formControlName]="data.ctrlName"
            placeholder="Silahkan input target"
            appNumberInputFormat
            ngDefaultControl
          />
          <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.QTY_QR" class="input-group-text text-capitalize fs-8 text-gray-700">QR</span>
          <span *ngIf="TypeTargetScan?.value === EnumProgramRetailerTargetScanType.VOLUME" class="input-group-text text-capitalize fs-8 text-gray-700">
            {{ data.deliveryUnit }}
          </span>
        </div>
      </fieldset>
      <ng-container *ngIf="index > 0">
        <span *ngIf="data.fg.get('value')?.hasError('notHigherThanPrevious')" class="fs-8 text-danger mt-2 col-12 col-lg-7 offset-lg-5 mt-2 animate__animated animate__fadeIn"
          >Target harus melebihi Tingkat {{ index }}</span
        >
      </ng-container>
    </ng-template>
  </div>
</ng-template>

<ng-template #noteViewInfoTpl let-data="data">
  <app-note-view [color]="'info'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [classNoteView]="'my-6 animate__animated animate__fadeIn'" [text]="data" />
</ng-template>

<ng-template #loaderTpl>
  <div><app-section-loader /></div>
</ng-template>
