import { StringUtilsService } from '../string-utils.service';

describe('StringUtilsService', () => {
  let service: StringUtilsService;

  beforeEach(() => {
    service = new StringUtilsService();
  });

  describe('arrayStringJoin', () => {
    it('should join array of objects by key with default separator', () => {
      const data = [{ name: 'A' }, { name: 'B' }];
      expect(service.arrayStringJoin(data, 'name')).toBe('A,B');
    });

    it('should join array of strings directly if key is null', () => {
      expect(service.arrayStringJoin(['A', 'B', 'C'], null)).toBe('A,B,C');
    });

    it('should include space if spaceBefore is true', () => {
      expect(service.arrayStringJoin(['X', 'Y'], null, '|', true)).toBe('X| Y');
    });

    it('should return empty string if array is falsy', () => {
      expect(service.arrayStringJoin(null as any, 'name')).toBe('');
    });
  });

  describe('toCapitalize', () => {
    it('should capitalize each word in string', () => {
      expect(service.toCapitalize('hello world')).toBe('Hello World');
    });

    it('should return empty string if input is empty', () => {
      expect(service.toCapitalize('')).toBe('');
    });
  });

  describe('switchDotAndComma', () => {
    it('should switch commas to dots and dots to commas', () => {
      expect(service.switchDotAndComma('1,23.456')).toBe('1.23,456');
    });

    it('should return "0" if input is falsy', () => {
      expect(service.switchDotAndComma('')).toBe('0');
    });
  });

  describe('switchDotToComma', () => {
    it('should switch dots to commas', () => {
      expect(service.switchDotToComma('1.23')).toBe('1,23');
    });
  });

  describe('switchCommaToDot', () => {
    it('should switch commas to dots', () => {
      expect(service.switchCommaToDot('1,23')).toBe('1.23');
    });
  });

  describe('filterStringArray', () => {
    it('should return items containing the filter string', () => {
      const result = service.filterStringArray(['apple', 'banana', 'grape'], 'ap');
      expect(result).toEqual(['apple', 'grape']);
    });

    it('should return empty array if no items match', () => {
      const result = service.filterStringArray(['one', 'two'], 'zzz');
      expect(result).toEqual([]);
    });
  });

  describe('getTypeDoc', () => {
    it('should return "FILE" for URL containing "/FILE_"', () => {
      const url = 'https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/FILE_92364ae1';
      expect(service.getTypeDoc(url)).toBe('FILE');
    });

    it('should return "IMAGE" for URL containing "/IMAGE_"', () => {
      const url = 'https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_92364ae1';
      expect(service.getTypeDoc(url)).toBe('IMAGE');
    });

    it('should return undefined for URL not containing FILE or IMAGE prefix', () => {
      const url = 'https://example.com/user/DOCUMENT_abc';
      expect(service.getTypeDoc(url)).toBeUndefined();
    });

    it('should return undefined for empty string', () => {
      expect(service.getTypeDoc('')).toBeUndefined();
    });

    it('should return FILE or IMAGE case-insensitive', () => {
      const lowerFile = 'https://example.com/user/file_123';
      const lowerImage = 'https://example.com/user/image_123';

      expect(service.getTypeDoc(lowerFile)).toBe('FILE');
      expect(service.getTypeDoc(lowerImage)).toBe('IMAGE');
    });
  });
});
