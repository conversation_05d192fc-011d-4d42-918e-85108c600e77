# Example: Testing Pipeline Configuration
# Rename to testing.yml and uncomment include in main .gitlab-ci.yml when ready

# Unit Tests
unit-tests:
  stage: test
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🧪 Running unit tests..."
    - npm run test:no-watch
    - echo "📊 Generating coverage report..."
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      junit: coverage/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - merge_requests
    - development
    - staging
    - main

# E2E Tests
e2e-tests:
  stage: test
  image: cypress/included:12.0.0
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "🎭 Running E2E tests..."
    - npm run e2e:headless
  artifacts:
    when: always
    paths:
      - cypress/screenshots/
      - cypress/videos/
    expire_in: 1 week
  cache:
    key: ${CI_COMMIT_REF_SLUG}-npm
    paths:
      - .npm/
  only:
    - development
    - staging
  allow_failure: true

# Performance Tests
performance-tests:
  stage: test
  image: node:18
  before_script:
    - npm ci --cache .npm --prefer-offline
  script:
    - echo "⚡ Running performance tests..."
    - npm run test:performance
  artifacts:
    reports:
      performance: performance-report.json
    expire_in: 1 week
  only:
    - staging
    - main
  allow_failure: true

# Test Summary
test-summary:
  stage: test
  image: alpine:latest
  script:
    - echo "📋 Test Summary"
    - echo "✅ Unit tests completed"
    - echo "✅ E2E tests completed"
    - echo "✅ Performance tests completed"
  needs:
    - unit-tests
    - e2e-tests
    - performance-tests
  when: always
