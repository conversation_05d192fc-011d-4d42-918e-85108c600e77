# NPM Dependency Issues - Resolution Guide

## 📦 Common NPM Dependency Problems

This directory contains resolutions for NPM package management issues, version conflicts, and installation problems.

## 🔍 Issue Categories

### Version Conflicts
- Peer dependency resolution errors
- Package compatibility issues
- Angular version mismatches
- Transitive dependency conflicts

### Installation Failures
- ERESOLVE errors
- Network timeouts
- Permission issues
- Cache corruption

### Package Management
- Outdated package versions
- Security vulnerabilities
- License conflicts
- Bundle size optimization

## 📋 Resolution Index

| Issue ID | Title | Angular Version | Status | Date |
|----------|-------|----------------|--------|------|
| [RES-001](./RES-001-angular-in-memory-web-api-eresolve.md) | Angular-in-memory-web-api ERESOLVE conflict | 18.2.13 | ✅ Resolved | 2025-06-21 |

## 🛠️ Quick Solutions

### ERESOLVE Errors
```bash
# Try legacy peer deps first
npm install --legacy-peer-deps

# If that fails, use force (more aggressive)
npm install --force

# Make permanent with .npmrc
echo "legacy-peer-deps=true" > .npmrc
```

### Package Version Conflicts
```bash
# Check available versions
npm view package-name versions --json

# Install specific version
npm install package-name@version

# Check peer dependencies
npm info package-name peerDependencies
```

### Cache Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 📊 Angular Compatibility Matrix

### Angular 18 Compatible Packages
| Package | Compatible Version | Notes |
|---------|-------------------|-------|
| `angular-in-memory-web-api` | `^0.18.0` | ✅ Angular 18 support |
| `ng-inline-svg-2` | `^19.0.0` | ✅ Latest version |
| `@angular/cdk` | `^18.2.14` | ✅ Perfect match |
| `@angular/material` | `^18.2.14` | ✅ Perfect match |

### Angular 17 Compatible Packages
| Package | Compatible Version | Notes |
|---------|-------------------|-------|
| `angular-in-memory-web-api` | `^0.17.0` | ✅ Angular 17 support |
| `ng-inline-svg-2` | `^17.0.0` | ✅ Version aligned |

## 🚨 Common Pitfalls

### Version Misalignment
- Always check Angular version compatibility
- Use exact versions for critical packages
- Test thoroughly after version updates

### Peer Dependency Hell
- Understand the difference between dependencies and peerDependencies
- Use `--legacy-peer-deps` as a temporary solution
- Plan for proper version alignment

### Cache Corruption
- Clear cache when experiencing unexplained issues
- Use `npm ci` in CI/CD for consistent installs
- Consider using `yarn` as alternative package manager

## 📝 Best Practices

### Before Installing Packages
1. Check Angular compatibility
2. Review package maintenance status
3. Check for security vulnerabilities
4. Consider bundle size impact

### During Installation
1. Use exact versions for stability
2. Document version decisions
3. Test in isolated environment first
4. Update package-lock.json

### After Installation
1. Run full test suite
2. Check bundle size impact
3. Verify no new vulnerabilities
4. Update documentation

## 🔧 Troubleshooting Workflow

```mermaid
graph TD
    A[NPM Install Fails] --> B{ERESOLVE Error?}
    B -->|Yes| C[Try --legacy-peer-deps]
    B -->|No| D{Network Error?}
    C --> E{Success?}
    E -->|Yes| F[Create .npmrc]
    E -->|No| G[Try --force]
    D -->|Yes| H[Check network/proxy]
    D -->|No| I[Clear cache]
    G --> J{Success?}
    J -->|Yes| K[Document solution]
    J -->|No| L[Check package versions]
    F --> K
    H --> A
    I --> A
    L --> M[Update to compatible versions]
    M --> A
```

## 📞 Escalation

### When to Escalate
- Multiple failed resolution attempts
- Security vulnerabilities in required packages
- Breaking changes affecting core functionality
- Time-sensitive deployment blockers

### Escalation Contacts
- **Tech Lead**: Complex dependency conflicts
- **Security Team**: Vulnerability-related issues
- **DevOps**: CI/CD pipeline problems
- **Architecture Team**: Major version upgrades

---

**Last Updated**: 2025-06-21  
**Maintained By**: Development Team
