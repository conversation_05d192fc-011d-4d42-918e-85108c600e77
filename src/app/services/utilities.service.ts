import { Injectable } from '@angular/core';
import { StringUtilsService } from '@utils/string-utils.service';
import { NumberUtilsService } from '@utils/number-utils.service';
import { DateUtilsService } from '@utils/date-utils.service';
import { DomUtilsService } from '@utils/dom-utils.service';
import { EnumUtilsService } from '@utils/enum-utils.service';
import { ObjectUtilsService } from '@utils/object-utils.service';
import { PrivilegeUtilsService } from '@utils/privilege-utils.service';
import { RenderUtilsService } from '@utils/render-utils.service';
import { DataDiffUtilsService } from '@utils/data-diff-utils.service';
import { FileUtilsService } from '@utils/file-utils.service';

@Injectable({
  providedIn: 'root',
})
export class UtilitiesService {
  constructor(
    private stringUtils: StringUtilsService,
    private numberUtils: NumberUtilsService,
    private dateUtils: DateUtilsService,
    private domUtils: DomUtilsService,
    private enumUtils: EnumUtilsService,
    private objectUtils: ObjectUtilsService,
    private privilegeUtils: PrivilegeUtilsService,
    private renderUtils: RenderUtilsService,
    private dataDiffUtils: DataDiffUtilsService,
    private fileUtils: FileUtilsService
  ) {}

  // String
  arrayStringJoin = this.stringUtils.arrayStringJoin;
  toCapitalize = this.stringUtils.toCapitalize;
  switchDotAndComma = this.stringUtils.switchDotAndComma;
  switchDotToComma = this.stringUtils.switchDotToComma;
  switchCommaToDot = this.stringUtils.switchCommaToDot;
  filterStringArray = this.stringUtils.filterStringArray;
  getTypeDoc = this.stringUtils.getTypeDoc;

  // Number
  convertTwoDigits = this.numberUtils.convertTwoDigits;
  convertStringToNumberWithComma = this.numberUtils.convertStringToNumberWithComma;
  toRupiah = this.numberUtils.toRupiah;
  toThousandConvert = this.numberUtils.toThousandConvert;
  toStringWithUnitType = this.numberUtils.toStringWithUnitType;
  toNumberFormat = this.numberUtils.toNumberFormat;
  stringNumberToPayload = this.numberUtils.stringNumberToPayload;
  formatInternationalNumber = this.numberUtils.formatInternationalNumber;
  reverseFormatInternationalNumber = this.numberUtils.reverseFormatInternationalNumber;

  // Date
  timeStampToDate = this.dateUtils.timeStampToDate;
  dateStringToEpoch = this.dateUtils.dateStringToEpoch;
  formatEpochToDate = this.dateUtils.formatEpochToDate;
  formatEpochTime = this.dateUtils.formatEpochTime;
  calculateDateDiff = this.dateUtils.calculateDateDiff;
  countDays = this.dateUtils.countDays;

  // DOM
  reloadPage = this.domUtils.reloadPage;
  copyToClipboard = this.domUtils.copyToClipboard;
  setDefaultImageProduct = this.domUtils.setDefaultImageProduct;
  otherToggleSelection = this.domUtils.otherToggleSelection;
  getBrowserName = this.domUtils.getBrowserName;

  // Enum
  mapKeyToString = this.enumUtils.mapKeyToString;
  getEnumKeyByValue = this.enumUtils.getEnumKeyByValue;
  SubMenuBreadcrumbTitles = this.enumUtils.SubMenuBreadcrumbTitles;

  // Object
  removeDuplicateByKey = this.objectUtils.removeDuplicateByKey;
  objectMapToArray = this.objectUtils.objectMapToArray;
  isObjectEmpty = this.objectUtils.isObjectEmpty;
  isArrayObjIdentical = this.objectUtils.isArrayObjIdentical;

  // Privilege
  privilegeTableColumns = this.privilegeUtils.privilegeTableColumns;
  privilegeConfidentialDocuments = this.privilegeUtils.privilegeConfidentialDocuments;
  getStatusDetailPrivilage = this.privilegeUtils.getStatusDetailPrivilege;

  // Render
  renderStringList = this.renderUtils.renderStringList;
  checkInHasNewValue = this.renderUtils.checkInHasNewValue;

  // Diff
  generateUpdateValue = this.dataDiffUtils.generateUpdateValue;
  generateUpdateEditData = this.dataDiffUtils.generateUpdateEditData;
  generateSideDocument = this.dataDiffUtils.generateSideDocument;

  // File
  downloadFile = this.fileUtils.downloadFile;
  convertToCSV = this.fileUtils.convertToCSV;
  parseCsvToJson = this.fileUtils.parseCsvToJson;
}
