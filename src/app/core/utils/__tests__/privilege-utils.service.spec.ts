import { PrivilegeUtilsService } from '../privilege-utils.service';

describe('PrivilegeUtilsService', () => {
  let service: PrivilegeUtilsService;

  beforeEach(() => {
    service = new PrivilegeUtilsService();
  });

  describe('privilegeTableColumns', () => {
    it('should add "actions" column if privilege is true and not already present', () => {
      const columns = [{ key: 'name', title: 'Name' }];
      const result = service.privilegeTableColumns(true, columns);
      expect(result).toContainEqual({ key: 'actions', title: 'Actions' });
    });

    it('should not add "actions" column if already present', () => {
      const columns = [
        { key: 'name', title: 'Name' },
        { key: 'actions', title: 'Actions' },
      ];
      const result = service.privilegeTableColumns(true, columns);
      expect(result).toEqual(columns); // unchanged
    });

    it('should return original if privilege is false', () => {
      const columns = [{ key: 'name', title: 'Name' }];
      const result = service.privilegeTableColumns(false, columns);
      expect(result).toEqual(columns);
    });
  });

  describe('privilegeConfidentialDocuments', () => {
    const confidentials = [
      { name: 'SECRET', label: 'Secret', value: 'secret' },
      { name: 'CONFIDENTIAL', label: 'Confidential', value: 'conf' },
      { label: 'Unknown', value: 'unknown' }, // no name
    ];

    const privileges = [{ name: 'SECRET' }];

    it('should filter confidentials by matching privilege name', () => {
      const result = service.privilegeConfidentialDocuments(privileges, confidentials);
      expect(result).toContainEqual(confidentials[0]); // SECRET
      expect(result).toContainEqual(confidentials[2]); // no name
      expect(result).not.toContainEqual(confidentials[1]); // CONFIDENTIAL not in privileges
    });

    it('should return empty array if privileges or confidentials are empty', () => {
      expect(service.privilegeConfidentialDocuments([], confidentials)).toEqual([]);
      expect(service.privilegeConfidentialDocuments(privileges, [])).toEqual([]);
    });
  });

  describe('getStatusDetailPrivilege', () => {
    const DetailEnum = {
      PENDING: 'Pending',
      APPROVED: 'Approved',
    };

    it('should return mapped value from enum if exists', () => {
      expect(service.getStatusDetailPrivilege('PENDING', DetailEnum)).toBe('Pending');
    });

    it('should return null if status is falsy', () => {
      expect(service.getStatusDetailPrivilege('', DetailEnum)).toBeNull();
      expect(service.getStatusDetailPrivilege(null, DetailEnum)).toBeNull();
      expect(service.getStatusDetailPrivilege(undefined, DetailEnum)).toBeNull();
    });

    it('should return null if status not in enum', () => {
      expect(service.getStatusDetailPrivilege('REJECTED', DetailEnum)).toBeNull();
    });
  });
});
